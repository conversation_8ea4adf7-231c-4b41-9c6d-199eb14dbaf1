"""
NEURA AI Assistant - Main Entry Point
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/neura.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


def check_dependencies():
    """Check if all required dependencies are installed."""
    required_packages = [
        'transformers',
        'torch',
        'speech_recognition',
        'pyttsx3',
        'pymongo',
        'mongoengine',
        'requests',
        'pydantic',
        'dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Missing required packages: {', '.join(missing_packages)}")
        logger.error("Please install missing packages using: pip install -r requirements.txt")
        return False
    
    return True


def setup_environment():
    """Setup environment and check configuration."""
    # Check if .env file exists
    env_file = Path('.env')
    if not env_file.exists():
        logger.warning(".env file not found. Creating from template...")
        
        # Copy .env.example to .env
        example_file = Path('.env.example')
        if example_file.exists():
            import shutil
            shutil.copy(example_file, env_file)
            logger.info("Created .env file from template. Please edit it with your API keys.")
        else:
            logger.error(".env.example file not found!")
            return False
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check critical environment variables
    hf_token = os.getenv('HUGGINGFACE_API_TOKEN')
    if not hf_token or hf_token == 'your_huggingface_token_here':
        logger.warning("Hugging Face API token not configured. Some AI features may not work.")
        logger.warning("Please set HUGGINGFACE_API_TOKEN in your .env file.")
    
    return True


def initialize_database():
    """Initialize the database."""
    try:
        from config.mongodb_models import init_mongodb
        init_mongodb()
        logger.info("MongoDB initialized successfully")
        return True
    except Exception as e:
        logger.error(f"MongoDB initialization failed: {e}")
        return False


def main():
    """Main entry point for NEURA AI Assistant."""
    logger.info("Starting NEURA AI Assistant...")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        sys.exit(1)
    
    # Initialize database
    if not initialize_database():
        sys.exit(1)
    
    # Check command line arguments
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == 'cli':
            # Command line interface mode
            from cli_interface import run_cli
            run_cli()
        elif mode == 'voice':
            # Voice-only mode
            from voice_interface import run_voice_mode
            run_voice_mode()
        elif mode == 'test':
            # Test mode
            from tests.test_runner import run_tests
            run_tests()
        else:
            logger.error(f"Unknown mode: {mode}")
            logger.info("Available modes: gui (default), cli, voice, test")
            sys.exit(1)
    else:
        # Default GUI mode
        try:
            from gui.main_window import main as gui_main
            gui_main()
        except ImportError as e:
            logger.error(f"GUI dependencies not available: {e}")
            logger.info("Falling back to CLI mode...")
            from cli_interface import run_cli
            run_cli()
        except Exception as e:
            logger.error(f"Failed to start GUI: {e}")
            sys.exit(1)


if __name__ == "__main__":
    main()
