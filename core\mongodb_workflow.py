"""
MongoDB Workflow Management System for NEURA AI Assistant
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from bson import ObjectId

from config.mongodb_models import User, Workflow, WorkflowStep
from config.settings import get_settings

logger = logging.getLogger(__name__)


class MongoWorkflowManager:
    """MongoDB-based workflow manager for NEURA AI Assistant."""
    
    def __init__(self):
        self.settings = get_settings()
        self.current_user: Optional[User] = None
        
        # Predefined workflow templates
        self.workflow_templates = {
            "freelance_business": {
                "name": "Start a Freelance Business",
                "description": "Complete guide to starting your freelance business",
                "steps": [
                    {"title": "Choose Your Niche", "description": "Identify your skills and target market"},
                    {"title": "Build a Portfolio", "description": "Create samples of your best work"},
                    {"title": "Create Personal Brand", "description": "Design logo, website, and social media presence"},
                    {"title": "Set Up Business Structure", "description": "Register business, get licenses, set up accounting"},
                    {"title": "Establish Payment Systems", "description": "Set up invoicing and payment processing"},
                    {"title": "Find Your First Clients", "description": "Network, apply to jobs, and market your services"},
                    {"title": "Deliver and Scale", "description": "Complete projects and grow your client base"}
                ]
            },
            "uk_visa_application": {
                "name": "UK Visa Application Process",
                "description": "Step-by-step guide for UK visa application",
                "steps": [
                    {"title": "Determine Visa Type", "description": "Research and select the appropriate visa category"},
                    {"title": "Gather Required Documents", "description": "Collect passport, photos, financial documents, etc."},
                    {"title": "Complete Online Application", "description": "Fill out the visa application form online"},
                    {"title": "Pay Application Fee", "description": "Submit payment for visa processing"},
                    {"title": "Book Biometric Appointment", "description": "Schedule appointment at visa application center"},
                    {"title": "Attend Biometric Appointment", "description": "Provide fingerprints and photograph"},
                    {"title": "Wait for Decision", "description": "Track application status and wait for response"},
                    {"title": "Collect Passport", "description": "Retrieve passport with visa decision"}
                ]
            },
            "website_creation": {
                "name": "Create a Professional Website",
                "description": "Build a website from planning to launch",
                "steps": [
                    {"title": "Define Website Goals", "description": "Determine purpose, audience, and objectives"},
                    {"title": "Plan Site Structure", "description": "Create sitemap and wireframes"},
                    {"title": "Choose Platform", "description": "Select CMS, hosting, and domain name"},
                    {"title": "Design Interface", "description": "Create visual design and user experience"},
                    {"title": "Develop Content", "description": "Write copy, gather images, and create media"},
                    {"title": "Build and Test", "description": "Code the site and test functionality"},
                    {"title": "Launch and Optimize", "description": "Go live and monitor performance"}
                ]
            },
            "tax_filing": {
                "name": "File Annual Tax Return",
                "description": "Complete tax filing process",
                "steps": [
                    {"title": "Gather Tax Documents", "description": "Collect W-2s, 1099s, receipts, and records"},
                    {"title": "Choose Filing Method", "description": "Decide on software, professional, or paper filing"},
                    {"title": "Review Previous Year", "description": "Check last year's return for reference"},
                    {"title": "Complete Tax Forms", "description": "Fill out all required tax forms and schedules"},
                    {"title": "Review and Double-Check", "description": "Verify all information and calculations"},
                    {"title": "Submit Return", "description": "File electronically or mail paper return"},
                    {"title": "Track Refund/Payment", "description": "Monitor refund status or make payment if owed"}
                ]
            }
        }
    
    def set_current_user(self, user_id: str) -> None:
        """Set the current user for workflow operations."""
        try:
            if isinstance(user_id, str):
                self.current_user = User.objects(id=ObjectId(user_id)).first()
            else:
                self.current_user = User.objects(id=user_id).first()
        except Exception as e:
            logger.error(f"Error setting current user: {e}")
            self.current_user = None
    
    def create_workflow_from_template(self, template_name: str) -> Optional[Workflow]:
        """Create a new workflow from a predefined template."""
        if not self.current_user:
            raise ValueError("No current user set")
        
        if template_name not in self.workflow_templates:
            return None
        
        template = self.workflow_templates[template_name]
        
        try:
            # Create workflow steps
            steps = []
            for i, step_data in enumerate(template["steps"]):
                step = WorkflowStep(
                    step_number=i + 1,
                    title=step_data["title"],
                    description=step_data["description"],
                    status="pending"
                )
                steps.append(step)
            
            workflow = Workflow(
                user=self.current_user,
                name=template["name"],
                description=template["description"],
                total_steps=len(template["steps"]),
                steps_data=template["steps"],
                steps=steps,
                status="active"
            )
            
            workflow.save()
            logger.info(f"Created workflow '{template['name']}' for user {self.current_user.id}")
            return workflow
        except Exception as e:
            logger.error(f"Error creating workflow: {e}")
            return None
    
    def create_custom_workflow(
        self, 
        name: str, 
        description: str, 
        steps_data: List[Dict[str, str]]
    ) -> Optional[Workflow]:
        """Create a custom workflow with user-defined steps."""
        if not self.current_user:
            raise ValueError("No current user set")
        
        try:
            # Create workflow steps
            steps = []
            for i, step_data in enumerate(steps_data):
                step = WorkflowStep(
                    step_number=i + 1,
                    title=step_data.get("title", f"Step {i + 1}"),
                    description=step_data.get("description", ""),
                    status="pending"
                )
                steps.append(step)
            
            workflow = Workflow(
                user=self.current_user,
                name=name,
                description=description,
                total_steps=len(steps_data),
                steps_data=steps_data,
                steps=steps,
                status="active"
            )
            
            workflow.save()
            logger.info(f"Created custom workflow '{name}' for user {self.current_user.id}")
            return workflow
        except Exception as e:
            logger.error(f"Error creating custom workflow: {e}")
            return None
    
    def get_active_workflows(self) -> List[Workflow]:
        """Get all active workflows for the current user."""
        if not self.current_user:
            return []
        
        try:
            return list(Workflow.objects(user=self.current_user, status="active").order_by('-updated_at'))
        except Exception as e:
            logger.error(f"Error getting active workflows: {e}")
            return []
    
    def get_workflow(self, workflow_id: str) -> Optional[Workflow]:
        """Get a specific workflow by ID."""
        if not self.current_user:
            return None
        
        try:
            return Workflow.objects(id=ObjectId(workflow_id), user=self.current_user).first()
        except Exception as e:
            logger.error(f"Error getting workflow: {e}")
            return None
    
    def get_workflow_steps(self, workflow_id: str) -> List[WorkflowStep]:
        """Get all steps for a workflow."""
        try:
            workflow = Workflow.objects(id=ObjectId(workflow_id)).first()
            if workflow:
                return sorted(workflow.steps, key=lambda x: x.step_number)
            return []
        except Exception as e:
            logger.error(f"Error getting workflow steps: {e}")
            return []
    
    def start_step(self, workflow_id: str, step_number: int) -> bool:
        """Mark a workflow step as started."""
        try:
            workflow = Workflow.objects(id=ObjectId(workflow_id), user=self.current_user).first()
            if not workflow:
                return False
            
            # Find and update the step
            for step in workflow.steps:
                if step.step_number == step_number:
                    step.status = "in_progress"
                    step.started_at = datetime.utcnow()
                    break
            
            # Update workflow current step
            workflow.current_step = step_number
            workflow.save()
            
            logger.info(f"Started step {step_number} of workflow {workflow_id}")
            return True
        except Exception as e:
            logger.error(f"Error starting step: {e}")
            return False
    
    def complete_step(self, workflow_id: str, step_number: int) -> bool:
        """Mark a workflow step as completed."""
        try:
            workflow = Workflow.objects(id=ObjectId(workflow_id), user=self.current_user).first()
            if not workflow:
                return False
            
            # Find and update the step
            for step in workflow.steps:
                if step.step_number == step_number:
                    step.status = "complete"
                    step.completed_at = datetime.utcnow()
                    break
            
            # Check if all steps are completed
            completed_steps = sum(1 for step in workflow.steps if step.status == "complete")
            
            if completed_steps >= workflow.total_steps:
                workflow.status = "completed"
                workflow.completed_at = datetime.utcnow()
            else:
                workflow.current_step = step_number + 1
            
            workflow.save()
            logger.info(f"Completed step {step_number} of workflow {workflow_id}")
            return True
        except Exception as e:
            logger.error(f"Error completing step: {e}")
            return False
    
    def pause_workflow(self, workflow_id: str) -> bool:
        """Pause a workflow."""
        try:
            workflow = Workflow.objects(id=ObjectId(workflow_id), user=self.current_user).first()
            if workflow:
                workflow.status = "paused"
                workflow.save()
                logger.info(f"Paused workflow {workflow_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error pausing workflow: {e}")
            return False
    
    def resume_workflow(self, workflow_id: str) -> bool:
        """Resume a paused workflow."""
        try:
            workflow = Workflow.objects(id=ObjectId(workflow_id), user=self.current_user).first()
            if workflow and workflow.status == "paused":
                workflow.status = "active"
                workflow.save()
                logger.info(f"Resumed workflow {workflow_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error resuming workflow: {e}")
            return False
    
    def get_workflow_progress(self, workflow_id: str) -> Dict[str, Any]:
        """Get detailed progress information for a workflow."""
        try:
            workflow = Workflow.objects(id=ObjectId(workflow_id)).first()
            if not workflow:
                return {}
            
            completed_steps = sum(1 for step in workflow.steps if step.status == "complete")
            in_progress_steps = sum(1 for step in workflow.steps if step.status == "in_progress")
            
            progress_percentage = (completed_steps / workflow.total_steps) * 100 if workflow.total_steps > 0 else 0
            
            return {
                "workflow_id": str(workflow.id),
                "name": workflow.name,
                "status": workflow.status,
                "current_step": workflow.current_step,
                "total_steps": workflow.total_steps,
                "completed_steps": completed_steps,
                "in_progress_steps": in_progress_steps,
                "progress_percentage": round(progress_percentage, 1),
                "created_at": workflow.created_at.isoformat(),
                "updated_at": workflow.updated_at.isoformat(),
                "steps": [
                    {
                        "step_number": step.step_number,
                        "title": step.title,
                        "description": step.description,
                        "status": step.status,
                        "started_at": step.started_at.isoformat() if step.started_at else None,
                        "completed_at": step.completed_at.isoformat() if step.completed_at else None
                    }
                    for step in sorted(workflow.steps, key=lambda x: x.step_number)
                ]
            }
        except Exception as e:
            logger.error(f"Error getting workflow progress: {e}")
            return {}
    
    def get_next_step_suggestion(self, workflow_id: str) -> Optional[str]:
        """Get suggestion for the next step in a workflow."""
        try:
            workflow = Workflow.objects(id=ObjectId(workflow_id)).first()
            if not workflow or workflow.status != "active":
                return None
            
            # Find the next pending step
            for step in sorted(workflow.steps, key=lambda x: x.step_number):
                if step.status == "pending":
                    return f"Ready to start: {step.title} - {step.description}"
            
            # Check if there's an in-progress step
            for step in workflow.steps:
                if step.status == "in_progress":
                    return f"Continue working on: {step.title} - {step.description}"
            
            # All steps completed
            return "Congratulations! You've completed all steps in this workflow."
        except Exception as e:
            logger.error(f"Error getting next step suggestion: {e}")
            return None
    
    def list_workflow_templates(self) -> List[Dict[str, str]]:
        """Get list of available workflow templates."""
        return [
            {
                "key": key,
                "name": template["name"],
                "description": template["description"],
                "steps_count": len(template["steps"])
            }
            for key, template in self.workflow_templates.items()
        ]
