"""
MongoDB Memory Management System for NEURA AI Assistant
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from bson import ObjectId

from config.mongodb_models import (
    User, Memory, Conversation, Workflow, WorkflowStep, 
    Reminder, get_user_by_email, create_user, get_user_memories,
    search_memories, cleanup_expired_memories
)
from config.settings import get_settings

logger = logging.getLogger(__name__)


class MongoMemoryManager:
    """MongoDB-based memory manager for NEURA AI Assistant."""
    
    def __init__(self):
        self.settings = get_settings()
        self.current_user: Optional[User] = None
        self.session_id: Optional[str] = None
    
    def set_current_user(self, user_id: str, session_id: str) -> None:
        """Set the current user and session."""
        try:
            if isinstance(user_id, str):
                self.current_user = User.objects(id=ObjectId(user_id)).first()
            else:
                self.current_user = User.objects(id=user_id).first()
            self.session_id = session_id
        except Exception as e:
            logger.error(f"Error setting current user: {e}")
            self.current_user = None
    
    def create_user(self, name: str, email: str, preferences: Dict = None) -> User:
        """Create a new user."""
        try:
            user = create_user(name, email, preferences)
            logger.info(f"Created new user: {name} ({email})")
            return user
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            raise
    
    def get_user(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        try:
            if isinstance(user_id, str):
                return User.objects(id=ObjectId(user_id)).first()
            return User.objects(id=user_id).first()
        except Exception as e:
            logger.error(f"Error getting user: {e}")
            return None
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        return get_user_by_email(email)
    
    def store_memory(
        self, 
        memory_type: str, 
        key: str, 
        value: str, 
        importance: float = 1.0,
        metadata: Dict = None,
        expires_in_days: Optional[int] = None
    ) -> Memory:
        """Store a memory for the current user."""
        if not self.current_user:
            raise ValueError("No current user set")
        
        expires_at = None
        if expires_in_days:
            expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
        
        try:
            # Check if memory already exists and update it
            existing = Memory.objects(
                user=self.current_user,
                memory_type=memory_type,
                key=key
            ).first()
            
            if existing:
                existing.value = value
                existing.importance = importance
                existing.metadata = metadata or {}
                existing.expires_at = expires_at
                existing.save()
                memory = existing
            else:
                memory = Memory(
                    user=self.current_user,
                    memory_type=memory_type,
                    key=key,
                    value=value,
                    importance=importance,
                    metadata=metadata or {},
                    expires_at=expires_at
                )
                memory.save()
            
            logger.info(f"Stored memory: {memory_type}/{key}")
            return memory
        except Exception as e:
            logger.error(f"Error storing memory: {e}")
            raise
    
    def retrieve_memory(
        self, 
        memory_type: Optional[str] = None, 
        key: Optional[str] = None,
        limit: int = 100
    ) -> List[Memory]:
        """Retrieve memories for the current user."""
        if not self.current_user:
            return []
        
        try:
            if key:
                # Get specific memory by key
                memories = Memory.objects(
                    user=self.current_user,
                    key=key
                )
                if memory_type:
                    memories = memories.filter(memory_type=memory_type)
                return list(memories.limit(limit))
            else:
                # Get memories by type or all
                return list(get_user_memories(self.current_user, memory_type, limit))
        except Exception as e:
            logger.error(f"Error retrieving memories: {e}")
            return []
    
    def search_memories(self, search_term: str, limit: int = 50) -> List[Memory]:
        """Search memories by content."""
        if not self.current_user:
            return []
        
        try:
            return list(search_memories(self.current_user, search_term, limit))
        except Exception as e:
            logger.error(f"Error searching memories: {e}")
            return []
    
    def store_conversation(
        self, 
        message_type: str, 
        content: str, 
        metadata: Dict = None
    ) -> Conversation:
        """Store a conversation message."""
        if not self.current_user or not self.session_id:
            raise ValueError("No current user or session set")
        
        try:
            conversation = Conversation(
                user=self.current_user,
                session_id=self.session_id,
                message_type=message_type,
                content=content,
                metadata=metadata or {}
            )
            conversation.save()
            return conversation
        except Exception as e:
            logger.error(f"Error storing conversation: {e}")
            raise
    
    def get_conversation_history(
        self, 
        session_id: Optional[str] = None, 
        limit: int = 50
    ) -> List[Conversation]:
        """Get conversation history."""
        if not self.current_user:
            return []
        
        target_session = session_id or self.session_id
        if not target_session:
            return []
        
        try:
            return list(Conversation.objects(
                user=self.current_user,
                session_id=target_session
            ).order_by('timestamp').limit(limit))
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    def get_recent_conversations(self, days: int = 7, limit: int = 100) -> List[Conversation]:
        """Get recent conversations across all sessions."""
        if not self.current_user:
            return []
        
        since_date = datetime.utcnow() - timedelta(days=days)
        
        try:
            return list(Conversation.objects(
                user=self.current_user,
                timestamp__gte=since_date
            ).order_by('-timestamp').limit(limit))
        except Exception as e:
            logger.error(f"Error getting recent conversations: {e}")
            return []
    
    def cleanup_expired_memories(self) -> int:
        """Remove expired memories and return count of removed items."""
        try:
            return cleanup_expired_memories()
        except Exception as e:
            logger.error(f"Error cleaning up expired memories: {e}")
            return 0
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        if not self.current_user:
            return {}
        
        try:
            total_memories = Memory.objects(user=self.current_user).count()
            
            # Get distinct memory types
            memory_types = Memory.objects(user=self.current_user).distinct('memory_type')
            
            # Get recent conversations count
            recent_conversations = Conversation.objects(
                user=self.current_user,
                timestamp__gte=datetime.utcnow() - timedelta(days=7)
            ).count()
            
            return {
                "total_memories": total_memories,
                "memory_types": memory_types,
                "recent_conversations": recent_conversations,
                "current_session": self.session_id
            }
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return {}
    
    def delete_memory(self, memory_id: str) -> bool:
        """Delete a specific memory."""
        if not self.current_user:
            return False
        
        try:
            memory = Memory.objects(
                id=ObjectId(memory_id),
                user=self.current_user
            ).first()
            
            if memory:
                memory.delete()
                logger.info(f"Deleted memory: {memory_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting memory: {e}")
            return False
    
    def update_memory(
        self, 
        memory_id: str, 
        value: Optional[str] = None,
        importance: Optional[float] = None,
        metadata: Optional[Dict] = None
    ) -> bool:
        """Update an existing memory."""
        if not self.current_user:
            return False
        
        try:
            memory = Memory.objects(
                id=ObjectId(memory_id),
                user=self.current_user
            ).first()
            
            if memory:
                if value is not None:
                    memory.value = value
                if importance is not None:
                    memory.importance = importance
                if metadata is not None:
                    memory.metadata = metadata
                
                memory.save()
                logger.info(f"Updated memory: {memory_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error updating memory: {e}")
            return False
