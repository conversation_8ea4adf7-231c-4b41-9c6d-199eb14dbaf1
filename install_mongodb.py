"""
MongoDB Installation Script for NEURA AI Assistant
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_header():
    """Print installation header."""
    print("🚀 NEURA AI Assistant - MongoDB Setup")
    print("=" * 50)

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def check_mongodb_connection():
    """Check if MongoDB is running and accessible."""
    print("🔍 Checking MongoDB connection...")
    
    try:
        import pymongo
        client = pymongo.MongoClient('mongodb://localhost:27017', serverSelectionTimeoutMS=5000)
        client.server_info()  # Will raise exception if can't connect
        print("✅ MongoDB is running and accessible!")
        return True
    except ImportError:
        print("⚠️ pymongo not installed yet - will install with dependencies")
        return True  # We'll install it later
    except Exception as e:
        print(f"❌ Cannot connect to MongoDB: {e}")
        print("📋 Please ensure:")
        print("   1. MongoDB is installed and running")
        print("   2. MongoDB Compass can connect to localhost:27017")
        print("   3. MongoDB service is started")
        return False

def install_dependencies():
    """Install Python dependencies."""
    print("📦 Installing Python dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install core dependencies first
        core_deps = [
            "pymongo>=4.5.0",
            "motor>=3.3.0", 
            "mongoengine>=0.27.0",
            "python-dotenv>=1.0.0",
            "pydantic>=2.0.0"
        ]
        
        print("Installing core MongoDB dependencies...")
        for dep in core_deps:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
        
        # Install all requirements
        print("Installing all requirements...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print("💡 Try running: pip install -r requirements.txt manually")
        return False

def setup_environment():
    """Setup environment configuration."""
    print("⚙️ Setting up environment...")
    
    env_file = Path(".env")
    example_file = Path(".env.example")
    
    if not env_file.exists() and example_file.exists():
        import shutil
        shutil.copy(example_file, env_file)
        print("✅ Created .env file from template")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("❌ .env.example file not found!")
        return False
    
    # Check if Hugging Face token is configured
    try:
        with open(env_file, 'r') as f:
            content = f.read()
            if 'your_huggingface_token_here' in content:
                print("⚠️ Please update your Hugging Face API token in .env file")
            else:
                print("✅ Hugging Face API token appears to be configured")
    except Exception as e:
        print(f"⚠️ Could not check .env file: {e}")
    
    return True

def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = ["logs", "data", "models", "temp"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ Directories created")
    return True

def test_mongodb_models():
    """Test MongoDB models and connection."""
    print("🧪 Testing MongoDB models...")
    
    try:
        from config.mongodb_models import User, Memory, init_mongodb
        
        # Test connection
        init_mongodb()
        
        # Test creating a user (will be cleaned up)
        test_user = User(name="Test User", email="<EMAIL>")
        test_user.save()
        
        # Test memory creation
        test_memory = Memory(
            user=test_user,
            memory_type="test",
            key="test_key",
            value="test_value"
        )
        test_memory.save()
        
        # Clean up test data
        test_memory.delete()
        test_user.delete()
        
        print("✅ MongoDB models working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ MongoDB models test failed: {e}")
        return False

def test_voice_dependencies():
    """Test voice-related dependencies."""
    print("🎤 Testing voice dependencies...")
    
    try:
        import speech_recognition
        import pyttsx3
        print("✅ Voice dependencies installed!")
        
        # Test TTS engine
        engine = pyttsx3.init()
        print("✅ Text-to-speech engine initialized!")
        
        return True
    except Exception as e:
        print(f"⚠️ Voice dependencies issue: {e}")
        print("💡 Voice features may not work properly")
        return True  # Don't fail installation for voice issues

def test_ai_dependencies():
    """Test AI-related dependencies."""
    print("🤖 Testing AI dependencies...")
    
    try:
        import transformers
        import torch
        print("✅ AI dependencies installed!")
        return True
    except Exception as e:
        print(f"⚠️ AI dependencies issue: {e}")
        print("💡 AI features may not work properly")
        return True  # Don't fail installation for AI issues

def print_next_steps():
    """Print next steps for the user."""
    print("\n🎉 Installation completed!")
    print("\n📋 Next steps:")
    print("1. Make sure MongoDB is running (check with MongoDB Compass)")
    print("2. Verify your Hugging Face API token in .env file")
    print("3. Connect your microphone and speakers")
    print("4. Run NEURA:")
    print("   - GUI mode: python main.py")
    print("   - CLI mode: python main.py cli") 
    print("   - Voice mode: python main.py voice")
    print("\n💡 First time? Try saying: 'Hey NEURA, help me start a freelance business'")
    print("\n📚 For detailed setup guide, see: MONGODB_SETUP.md")

def main():
    """Main installation function."""
    print_header()
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check MongoDB
    if not check_mongodb_connection():
        print("\n💡 Please start MongoDB and try again")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        sys.exit(1)
    
    # Test MongoDB models
    if not test_mongodb_models():
        print("⚠️ MongoDB test failed - please check your MongoDB setup")
    
    # Test voice dependencies
    test_voice_dependencies()
    
    # Test AI dependencies
    test_ai_dependencies()
    
    # Print next steps
    print_next_steps()

if __name__ == "__main__":
    main()
