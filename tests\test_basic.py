"""
Basic tests for NEURA AI Assistant
"""

import pytest
import asyncio
import tempfile
import os
from pathlib import Path

# Add project root to path
import sys
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.memory import MemoryManager
from core.workflow import WorkflowManager
from core.ai_engine import AI<PERSON>ngine
from core.neura_assistant import NeuraAssistant
from config.models import create_tables, engine


class TestMemoryManager:
    """Test memory management functionality."""
    
    def setup_method(self):
        """Setup test environment."""
        self.memory_manager = MemoryManager()
        
        # Create test user
        user = self.memory_manager.create_user("Test User", "<EMAIL>")
        self.memory_manager.set_current_user(user.id, "test-session")
    
    def test_store_and_retrieve_memory(self):
        """Test storing and retrieving memories."""
        # Store a memory
        memory = self.memory_manager.store_memory(
            "preference", 
            "favorite_color", 
            "blue", 
            importance=0.8
        )
        
        assert memory.key == "favorite_color"
        assert memory.value == "blue"
        assert memory.importance == 0.8
        
        # Retrieve memories
        memories = self.memory_manager.retrieve_memory("preference")
        assert len(memories) >= 1
        assert any(m.key == "favorite_color" for m in memories)
    
    def test_search_memories(self):
        """Test memory search functionality."""
        # Store multiple memories
        self.memory_manager.store_memory("goal", "career", "become a software engineer")
        self.memory_manager.store_memory("preference", "food", "pizza and pasta")
        
        # Search for memories
        results = self.memory_manager.search_memories("software")
        assert len(results) >= 1
        assert any("software" in m.value.lower() for m in results)
    
    def test_conversation_storage(self):
        """Test conversation history storage."""
        # Store conversation messages
        self.memory_manager.store_conversation("user", "Hello NEURA")
        self.memory_manager.store_conversation("assistant", "Hello! How can I help you?")
        
        # Retrieve conversation history
        history = self.memory_manager.get_conversation_history()
        assert len(history) >= 2
        assert history[0].message_type == "user"
        assert history[1].message_type == "assistant"


class TestWorkflowManager:
    """Test workflow management functionality."""
    
    def setup_method(self):
        """Setup test environment."""
        self.workflow_manager = WorkflowManager()
        
        # Create test user
        from core.memory import MemoryManager
        memory_manager = MemoryManager()
        user = memory_manager.create_user("Test User", "<EMAIL>")
        self.workflow_manager.set_current_user(user.id)
    
    def test_create_workflow_from_template(self):
        """Test creating workflow from template."""
        workflow = self.workflow_manager.create_workflow_from_template("freelance_business")
        
        assert workflow is not None
        assert workflow.name == "Start a Freelance Business"
        assert workflow.total_steps > 0
        assert workflow.status == "active"
    
    def test_workflow_progress_tracking(self):
        """Test workflow progress tracking."""
        # Create workflow
        workflow = self.workflow_manager.create_workflow_from_template("website_creation")
        
        # Start first step
        success = self.workflow_manager.start_step(workflow.id, 1)
        assert success
        
        # Complete first step
        success = self.workflow_manager.complete_step(workflow.id, 1)
        assert success
        
        # Check progress
        progress = self.workflow_manager.get_workflow_progress(workflow.id)
        assert progress["completed_steps"] == 1
        assert progress["progress_percentage"] > 0
    
    def test_custom_workflow_creation(self):
        """Test creating custom workflows."""
        steps = [
            {"title": "Step 1", "description": "First step"},
            {"title": "Step 2", "description": "Second step"},
            {"title": "Step 3", "description": "Third step"}
        ]
        
        workflow = self.workflow_manager.create_custom_workflow(
            "Test Workflow",
            "A test workflow",
            steps
        )
        
        assert workflow.name == "Test Workflow"
        assert workflow.total_steps == 3
        
        # Check steps were created
        workflow_steps = self.workflow_manager.get_workflow_steps(workflow.id)
        assert len(workflow_steps) == 3


class TestAIEngine:
    """Test AI engine functionality."""
    
    def setup_method(self):
        """Setup test environment."""
        self.ai_engine = AIEngine()
    
    @pytest.mark.asyncio
    async def test_generate_response(self):
        """Test response generation."""
        response = await self.ai_engine.generate_response("Hello, how are you?")
        
        assert isinstance(response, str)
        assert len(response) > 0
    
    @pytest.mark.asyncio
    async def test_conversational_responses(self):
        """Test various conversational inputs."""
        test_cases = [
            "help me with something",
            "what can you do",
            "remember that I like coffee",
            "start a workflow"
        ]
        
        for test_input in test_cases:
            response = await self.ai_engine.generate_response(test_input)
            assert isinstance(response, str)
            assert len(response) > 0


class TestNeuraAssistant:
    """Test main NEURA assistant functionality."""
    
    def setup_method(self):
        """Setup test environment."""
        self.neura = NeuraAssistant()
        self.neura.initialize_user("Test User", "<EMAIL>")
    
    @pytest.mark.asyncio
    async def test_message_processing(self):
        """Test message processing."""
        response = await self.neura.process_message("Hello NEURA!")
        
        assert isinstance(response, str)
        assert len(response) > 0
    
    @pytest.mark.asyncio
    async def test_workflow_commands(self):
        """Test workflow-related commands."""
        # Test workflow creation
        response = await self.neura.process_message("start workflow freelance business")
        assert "workflow" in response.lower()
        
        # Test workflow status
        response = await self.neura.process_message("workflow status")
        assert isinstance(response, str)
    
    @pytest.mark.asyncio
    async def test_memory_commands(self):
        """Test memory-related commands."""
        # Test memory storage
        response = await self.neura.process_message("remember that I like pizza")
        assert "remember" in response.lower()
        
        # Test memory recall
        response = await self.neura.process_message("what do you remember about me")
        assert isinstance(response, str)
    
    def test_session_management(self):
        """Test session management."""
        session_id = self.neura.start_session()
        assert isinstance(session_id, str)
        assert len(session_id) > 0
        
        summary = self.neura.get_session_summary()
        assert summary["session_id"] == session_id
        assert summary["is_active"] is True


def run_tests():
    """Run all tests."""
    pytest.main([__file__, "-v"])


if __name__ == "__main__":
    run_tests()
