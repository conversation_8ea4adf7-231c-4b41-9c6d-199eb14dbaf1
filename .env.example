# NEURA AI Assistant Configuration

# Hugging Face API Configuration
HUGGINGFACE_API_TOKEN=your_huggingface_token_here
HUGGINGFACE_API_URL=https://api-inference.huggingface.co/models

# Database Configuration
DATABASE_URL=sqlite:///neura_memory.db
DATABASE_ECHO=false

# Voice Configuration
VOICE_INPUT_DEVICE=default
VOICE_OUTPUT_DEVICE=default
VOICE_RATE=200
VOICE_VOLUME=0.9
VOICE_LANGUAGE=en

# AI Model Configuration
SUMMARIZATION_MODEL=facebook/bart-large-cnn
QA_MODEL=deepset/roberta-base-squad2
TABLE_MODEL=microsoft/tapex-large-finetuned-wtq

# Application Settings
DEBUG=false
LOG_LEVEL=INFO
MAX_MEMORY_ENTRIES=1000
WORKFLOW_REMINDER_HOURS=24

# GUI Settings
WINDOW_WIDTH=800
WINDOW_HEIGHT=600
THEME=dark

# Optional: External AI Services (if needed as fallback)
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
