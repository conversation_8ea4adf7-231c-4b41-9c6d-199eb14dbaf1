"""
Voice Input/Output System for NEURA AI Assistant
"""

import logging
import threading
import time
from typing import Optional, Callable, Dict, Any
import speech_recognition as sr
import pyttsx3
import sounddevice as sd
import soundfile as sf
from queue import Queue, Empty

from config.settings import get_settings

logger = logging.getLogger(__name__)


class VoiceInputHandler:
    """Enhanced speech-to-text input processing with improved accuracy."""

    def __init__(self):
        self.settings = get_settings()
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        self.is_listening = False
        self.callback: Optional[Callable[[str], None]] = None
        self.listen_thread: Optional[threading.Thread] = None

        # Enhanced recognition settings for better accuracy
        self.recognizer.energy_threshold = 300  # Minimum audio energy to consider for recording
        self.recognizer.dynamic_energy_threshold = True  # Automatically adjust energy threshold
        self.recognizer.pause_threshold = 0.8  # Seconds of non-speaking audio before phrase is complete
        self.recognizer.operation_timeout = None  # No timeout for listening
        self.recognizer.phrase_threshold = 0.3  # Minimum seconds of speaking audio before we consider the speaking audio a phrase
        self.recognizer.non_speaking_duration = 0.5  # Seconds of non-speaking audio to keep on both sides

        # Initialize microphone and adjust for ambient noise
        try:
            with self.microphone as source:
                logger.info("Adjusting for ambient noise... Please wait.")
                self.recognizer.adjust_for_ambient_noise(source, duration=2)
                logger.info(f"Energy threshold set to: {self.recognizer.energy_threshold}")
        except Exception as e:
            logger.error(f"Error initializing microphone: {e}")
            # Set default values if microphone initialization fails
            self.recognizer.energy_threshold = 4000
    
    def set_callback(self, callback: Callable[[str], None]) -> None:
        """Set callback function for when speech is recognized."""
        self.callback = callback
    
    def start_listening(self) -> None:
        """Start continuous listening for voice input."""
        if self.is_listening:
            return
        
        self.is_listening = True
        self.listen_thread = threading.Thread(target=self._listen_loop, daemon=True)
        self.listen_thread.start()
        logger.info("Voice input started")
    
    def stop_listening(self) -> None:
        """Stop listening for voice input."""
        self.is_listening = False
        if self.listen_thread:
            self.listen_thread.join(timeout=2.0)
        logger.info("Voice input stopped")
    
    def _listen_loop(self) -> None:
        """Main listening loop running in separate thread."""
        while self.is_listening:
            try:
                with self.microphone as source:
                    # Listen for audio with timeout
                    audio = self.recognizer.listen(source, timeout=1.0, phrase_time_limit=5.0)
                
                try:
                    # Recognize speech using Google Speech Recognition
                    text = self.recognizer.recognize_google(audio, language=self.settings.voice_language)
                    if text and self.callback:
                        self.callback(text)
                        logger.info(f"Recognized speech: {text}")
                
                except sr.UnknownValueError:
                    # Speech was unintelligible
                    pass
                except sr.RequestError as e:
                    logger.error(f"Speech recognition error: {e}")
                    time.sleep(1.0)
            
            except sr.WaitTimeoutError:
                # No speech detected within timeout
                pass
            except Exception as e:
                logger.error(f"Voice input error: {e}")
                time.sleep(1.0)
    
    def recognize_once(self, timeout: float = 5.0) -> Optional[str]:
        """Recognize speech once with timeout."""
        try:
            with self.microphone as source:
                logger.info("Listening for speech...")
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=10.0)
            
            text = self.recognizer.recognize_google(audio, language=self.settings.voice_language)
            logger.info(f"Recognized: {text}")
            return text
        
        except sr.WaitTimeoutError:
            logger.warning("Speech recognition timeout")
            return None
        except sr.UnknownValueError:
            logger.warning("Could not understand audio")
            return None
        except sr.RequestError as e:
            logger.error(f"Speech recognition error: {e}")
            return None


class VoiceOutputHandler:
    """Handles text-to-speech output."""
    
    def __init__(self):
        self.settings = get_settings()
        self.engine = pyttsx3.init()
        self.is_speaking = False
        self.speech_queue = Queue()
        self.speech_thread: Optional[threading.Thread] = None
        
        # Configure voice settings
        self._configure_voice()
        
        # Start speech processing thread
        self._start_speech_thread()
    
    def _configure_voice(self) -> None:
        """Configure voice engine settings."""
        # Set speech rate
        self.engine.setProperty('rate', self.settings.voice_rate)
        
        # Set volume
        self.engine.setProperty('volume', self.settings.voice_volume)
        
        # Try to set voice based on language
        voices = self.engine.getProperty('voices')
        if voices:
            # Find voice matching language preference
            target_voice = None
            for voice in voices:
                if self.settings.voice_language.lower() in voice.id.lower():
                    target_voice = voice
                    break
            
            if target_voice:
                self.engine.setProperty('voice', target_voice.id)
                logger.info(f"Voice set to: {target_voice.name}")
            else:
                logger.info("Using default voice")
    
    def _start_speech_thread(self) -> None:
        """Start the speech processing thread."""
        self.speech_thread = threading.Thread(target=self._speech_loop, daemon=True)
        self.speech_thread.start()
    
    def _speech_loop(self) -> None:
        """Main speech processing loop."""
        while True:
            try:
                # Get next speech item from queue
                speech_item = self.speech_queue.get(timeout=1.0)
                if speech_item is None:  # Shutdown signal
                    break
                
                text, priority = speech_item
                self.is_speaking = True
                
                try:
                    self.engine.say(text)
                    self.engine.runAndWait()
                    logger.info(f"Spoke: {text[:50]}...")
                except Exception as e:
                    logger.error(f"Speech output error: {e}")
                finally:
                    self.is_speaking = False
                    self.speech_queue.task_done()
            
            except Empty:
                continue
            except Exception as e:
                logger.error(f"Speech loop error: {e}")
    
    def speak(self, text: str, priority: int = 1) -> None:
        """Add text to speech queue."""
        if not text.strip():
            return
        
        # Clean up text for better speech
        clean_text = self._clean_text_for_speech(text)
        
        # Add to queue
        self.speech_queue.put((clean_text, priority))
        logger.info(f"Queued for speech: {clean_text[:50]}...")
    
    def speak_immediately(self, text: str) -> None:
        """Speak text immediately, interrupting current speech."""
        if not text.strip():
            return
        
        # Stop current speech
        self.stop_speaking()
        
        # Clear queue and add high priority item
        while not self.speech_queue.empty():
            try:
                self.speech_queue.get_nowait()
                self.speech_queue.task_done()
            except Empty:
                break
        
        self.speak(text, priority=0)
    
    def stop_speaking(self) -> None:
        """Stop current speech output."""
        try:
            self.engine.stop()
        except Exception as e:
            logger.error(f"Error stopping speech: {e}")
    
    def is_busy(self) -> bool:
        """Check if currently speaking or has queued speech."""
        return self.is_speaking or not self.speech_queue.empty()
    
    def _clean_text_for_speech(self, text: str) -> str:
        """Clean text for better speech synthesis."""
        # Remove markdown formatting
        text = text.replace('**', '').replace('*', '').replace('_', '')
        text = text.replace('#', '').replace('`', '')
        
        # Replace common abbreviations
        replacements = {
            'AI': 'A I',
            'API': 'A P I',
            'URL': 'U R L',
            'HTTP': 'H T T P',
            'JSON': 'J S O N',
            'XML': 'X M L',
            'SQL': 'S Q L',
            'UI': 'U I',
            'UX': 'U X'
        }
        
        for abbr, replacement in replacements.items():
            text = text.replace(abbr, replacement)
        
        return text
    
    def shutdown(self) -> None:
        """Shutdown the voice output handler."""
        self.speech_queue.put(None)  # Shutdown signal
        if self.speech_thread:
            self.speech_thread.join(timeout=2.0)


class VoiceManager:
    """Main voice interaction manager."""
    
    def __init__(self):
        self.input_handler = VoiceInputHandler()
        self.output_handler = VoiceOutputHandler()
        self.wake_word = "hey neura"
        self.is_active = False
        self.conversation_callback: Optional[Callable[[str], str]] = None
    
    def set_conversation_callback(self, callback: Callable[[str], str]) -> None:
        """Set callback for processing voice conversations."""
        self.conversation_callback = callback
    
    def start_voice_interaction(self) -> None:
        """Start voice interaction mode."""
        self.input_handler.set_callback(self._handle_voice_input)
        self.input_handler.start_listening()
        self.is_active = True
        logger.info("Voice interaction started")
    
    def stop_voice_interaction(self) -> None:
        """Stop voice interaction mode."""
        self.input_handler.stop_listening()
        self.is_active = False
        logger.info("Voice interaction stopped")
    
    def _handle_voice_input(self, text: str) -> None:
        """Handle recognized voice input."""
        text_lower = text.lower()
        
        # Check for wake word
        if self.wake_word in text_lower:
            # Extract command after wake word
            wake_index = text_lower.find(self.wake_word)
            command = text[wake_index + len(self.wake_word):].strip()
            
            if command and self.conversation_callback:
                # Process the command
                response = self.conversation_callback(command)
                if response:
                    self.output_handler.speak(response)
            elif not command:
                # Just wake word, acknowledge
                self.output_handler.speak("Yes, how can I help you?")
    
    def speak(self, text: str, immediate: bool = False) -> None:
        """Speak text using voice output."""
        if immediate:
            self.output_handler.speak_immediately(text)
        else:
            self.output_handler.speak(text)
    
    def listen_once(self, timeout: float = 5.0) -> Optional[str]:
        """Listen for speech once."""
        return self.input_handler.recognize_once(timeout)
    
    def is_speaking(self) -> bool:
        """Check if currently speaking."""
        return self.output_handler.is_busy()
    
    def shutdown(self) -> None:
        """Shutdown voice manager."""
        self.stop_voice_interaction()
        self.output_handler.shutdown()
