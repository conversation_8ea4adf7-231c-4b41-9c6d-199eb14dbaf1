"""
NEURA AI Assistant Configuration Settings
"""

import os
from typing import Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Hugging Face Configuration
    huggingface_api_token: str = Field(..., env="HUGGINGFACE_API_TOKEN")
    huggingface_api_url: str = Field(
        default="https://api-inference.huggingface.co/models",
        env="HUGGINGFACE_API_URL"
    )
    
    # Database Configuration
    database_type: str = Field(default="mongodb", env="DATABASE_TYPE")
    mongodb_url: str = Field(
        default="mongodb://localhost:27017",
        env="MONGODB_URL"
    )
    mongodb_database: str = Field(default="neura_db", env="MONGODB_DATABASE")
    database_url: str = Field(
        default="sqlite:///neura_memory.db",
        env="DATABASE_URL"
    )
    database_echo: bool = Field(default=False, env="DATABASE_ECHO")
    
    # Voice Configuration
    voice_input_device: str = Field(default="default", env="VOICE_INPUT_DEVICE")
    voice_output_device: str = Field(default="default", env="VOICE_OUTPUT_DEVICE")
    voice_rate: int = Field(default=200, env="VOICE_RATE")
    voice_volume: float = Field(default=0.9, env="VOICE_VOLUME")
    voice_language: str = Field(default="en", env="VOICE_LANGUAGE")
    
    # AI Model Configuration
    summarization_model: str = Field(
        default="facebook/bart-large-cnn",
        env="SUMMARIZATION_MODEL"
    )
    qa_model: str = Field(
        default="deepset/roberta-base-squad2",
        env="QA_MODEL"
    )
    table_model: str = Field(
        default="microsoft/tapex-large-finetuned-wtq",
        env="TABLE_MODEL"
    )
    
    # Application Settings
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    max_memory_entries: int = Field(default=1000, env="MAX_MEMORY_ENTRIES")
    workflow_reminder_hours: int = Field(default=24, env="WORKFLOW_REMINDER_HOURS")
    
    # GUI Settings
    window_width: int = Field(default=800, env="WINDOW_WIDTH")
    window_height: int = Field(default=600, env="WINDOW_HEIGHT")
    theme: str = Field(default="dark", env="THEME")
    
    # Optional External AI Services
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings


def update_setting(key: str, value) -> None:
    """Update a setting value."""
    if hasattr(settings, key):
        setattr(settings, key, value)
    else:
        raise ValueError(f"Setting '{key}' does not exist")


def get_database_path() -> str:
    """Get the database file path."""
    if settings.database_url.startswith("sqlite:///"):
        return settings.database_url[10:]  # Remove 'sqlite:///'
    return "neura_memory.db"


def ensure_directories() -> None:
    """Ensure required directories exist."""
    directories = [
        "logs",
        "data",
        "models",
        "temp"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


# Initialize directories on import
ensure_directories()
