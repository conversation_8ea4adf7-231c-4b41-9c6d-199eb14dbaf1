"""
Command Line Interface for NEURA AI Assistant
"""

import asyncio
import sys
from datetime import datetime
from core.neura_assistant import NeuraAssistant


class NeuraCliInterface:
    """Command line interface for NEURA."""
    
    def __init__(self):
        self.neura = NeuraAssistant()
        self.running = True
    
    def setup_user(self):
        """Setup user for CLI session."""
        print("🤖 NEURA AI Assistant - Command Line Interface")
        print("=" * 50)
        
        name = input("Enter your name (or press Enter for 'CLI User'): ").strip()
        if not name:
            name = "CLI User"
        
        email = input("Enter your email (or press Enter for '<EMAIL>'): ").strip()
        if not email:
            email = "<EMAIL>"
        
        try:
            user_id = self.neura.initialize_user(name, email)
            session_id = self.neura.start_session()
            print(f"✅ Welcome {name}! Session started: {session_id[:8]}...")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize user: {e}")
            return False
    
    def print_help(self):
        """Print help information."""
        print("\n📋 Available Commands:")
        print("  help, h          - Show this help message")
        print("  voice            - Start voice interaction mode")
        print("  workflows        - Show active workflows")
        print("  memories         - Show stored memories")
        print("  status           - Show session status")
        print("  clear            - Clear screen")
        print("  quit, exit, q    - Exit NEURA")
        print("\n💬 Or just type your message to chat with NEURA!")
        print("-" * 50)
    
    async def process_command(self, command):
        """Process a command or message."""
        command = command.strip()
        
        if not command:
            return
        
        # Handle special commands
        if command.lower() in ['help', 'h']:
            self.print_help()
            return
        
        if command.lower() in ['quit', 'exit', 'q']:
            self.running = False
            print("👋 Goodbye! Thanks for using NEURA!")
            return
        
        if command.lower() == 'clear':
            import os
            os.system('cls' if os.name == 'nt' else 'clear')
            return
        
        if command.lower() == 'voice':
            print("🎤 Starting voice mode... Say 'Hey NEURA' to interact.")
            print("Press Ctrl+C to stop voice mode.")
            try:
                self.neura.start_voice_mode()
                input("Press Enter to stop voice mode...")
                self.neura.stop_voice_mode()
                print("🔇 Voice mode stopped.")
            except KeyboardInterrupt:
                self.neura.stop_voice_mode()
                print("\n🔇 Voice mode stopped.")
            return
        
        if command.lower() == 'workflows':
            workflows = self.neura.workflow_manager.get_active_workflows()
            if workflows:
                print("\n📋 Active Workflows:")
                for wf in workflows:
                    progress = self.neura.workflow_manager.get_workflow_progress(wf.id)
                    print(f"  • {wf.name}: {progress['completed_steps']}/{progress['total_steps']} steps ({progress['progress_percentage']}%)")
            else:
                print("\n📋 No active workflows. Try saying 'start workflow' to create one!")
            return
        
        if command.lower() == 'memories':
            memories = self.neura.memory_manager.retrieve_memory(limit=10)
            if memories:
                print("\n🧠 Your Memories:")
                for mem in memories[:5]:
                    print(f"  • {mem.key}: {mem.value}")
            else:
                print("\n🧠 No memories stored yet. I'll remember important things as we chat!")
            return
        
        if command.lower() == 'status':
            summary = self.neura.get_session_summary()
            print(f"\n📊 Session Status:")
            print(f"  • Session ID: {summary['session_id'][:8]}...")
            print(f"  • User ID: {summary['user_id']}")
            print(f"  • Active: {summary['is_active']}")
            print(f"  • Messages: {summary['conversation_length']}")
            print(f"  • Last interaction: {summary['last_interaction']}")
            return
        
        # Process as regular message
        print("🤖 NEURA is thinking...")
        try:
            response = await self.neura.process_message(command)
            print(f"\n🤖 NEURA: {response}")
        except Exception as e:
            print(f"\n❌ Error: {e}")
    
    async def run(self):
        """Run the CLI interface."""
        if not self.setup_user():
            return
        
        self.print_help()
        
        print("\n💬 Start chatting with NEURA! (type 'help' for commands)")
        
        while self.running:
            try:
                # Get user input
                user_input = input("\n👤 You: ").strip()
                
                if user_input:
                    await self.process_command(user_input)
            
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye! Thanks for using NEURA!")
                break
            except EOFError:
                print("\n\n👋 Goodbye! Thanks for using NEURA!")
                break
            except Exception as e:
                print(f"\n❌ Unexpected error: {e}")
        
        # Cleanup
        self.neura.shutdown()


def run_cli():
    """Run the CLI interface."""
    cli = NeuraCliInterface()
    asyncio.run(cli.run())


if __name__ == "__main__":
    run_cli()
