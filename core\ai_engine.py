"""
AI Engine with Hugging Face Integration for NEURA AI Assistant
"""

import logging
import asyncio
import aiohttp
import json
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from config.settings import get_settings
from config.mongodb_models import AIModelUsage

logger = logging.getLogger(__name__)


class HuggingFaceClient:
    """Client for interacting with Hugging Face Inference API."""
    
    def __init__(self):
        self.settings = get_settings()
        self.api_token = self.settings.huggingface_api_token
        self.api_url = self.settings.huggingface_api_url
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }
    
    async def query_model(
        self, 
        model_name: str, 
        inputs: Union[str, Dict], 
        parameters: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Query a Hugging Face model."""
        url = f"{self.api_url}/{model_name}"
        
        payload = {
            "inputs": inputs
        }
        
        if parameters:
            payload["parameters"] = parameters
        
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=self.headers, json=payload) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        result = await response.json()
                        self._log_usage(model_name, "success", inputs, result, response_time)
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"HuggingFace API error {response.status}: {error_text}")
                        self._log_usage(model_name, "error", inputs, None, response_time, error_text)
                        return {"error": f"API error {response.status}: {error_text}"}
        
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"HuggingFace API request failed: {e}")
            self._log_usage(model_name, "error", inputs, None, response_time, str(e))
            return {"error": str(e)}
    
    def _log_usage(
        self, 
        model_name: str, 
        status: str, 
        inputs: Any, 
        outputs: Any, 
        response_time: float,
        error_message: Optional[str] = None
    ) -> None:
        """Log model usage for analytics."""
        try:
            input_tokens = len(str(inputs).split()) if inputs else 0
            output_tokens = len(str(outputs).split()) if outputs else 0

            usage = AIModelUsage(
                model_name=model_name,
                task_type="inference",
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                response_time=response_time,
                success=(status == "success"),
                error_message=error_message
            )
            usage.save()
        except Exception as e:
            logger.error(f"Failed to log model usage: {e}")


class AIEngine:
    """Main AI engine for NEURA with various AI capabilities."""
    
    def __init__(self):
        self.settings = get_settings()
        self.hf_client = HuggingFaceClient()
        
        # Model configurations
        self.models = {
            "summarization": self.settings.summarization_model,
            "qa": self.settings.qa_model,
            "table": self.settings.table_model
        }
    
    async def summarize_text(
        self, 
        text: str, 
        max_length: int = 150, 
        min_length: int = 30
    ) -> str:
        """Summarize text using Hugging Face model."""
        try:
            parameters = {
                "max_length": max_length,
                "min_length": min_length,
                "do_sample": False
            }
            
            result = await self.hf_client.query_model(
                self.models["summarization"],
                text,
                parameters
            )
            
            if "error" in result:
                return f"Summarization failed: {result['error']}"
            
            if isinstance(result, list) and len(result) > 0:
                return result[0].get("summary_text", "No summary generated")
            
            return "No summary generated"
        
        except Exception as e:
            logger.error(f"Summarization error: {e}")
            return f"Summarization failed: {str(e)}"
    
    async def answer_question(self, question: str, context: str) -> str:
        """Answer a question based on provided context."""
        try:
            inputs = {
                "question": question,
                "context": context
            }
            
            result = await self.hf_client.query_model(
                self.models["qa"],
                inputs
            )
            
            if "error" in result:
                return f"I couldn't find an answer: {result['error']}"
            
            if "answer" in result:
                confidence = result.get("score", 0)
                answer = result["answer"]
                
                if confidence > 0.5:
                    return answer
                else:
                    return f"I'm not very confident, but I think: {answer}"
            
            return "I couldn't find a clear answer to your question."
        
        except Exception as e:
            logger.error(f"Question answering error: {e}")
            return f"I encountered an error while trying to answer: {str(e)}"
    
    async def process_table_query(self, query: str, table_data: str) -> str:
        """Process queries about tabular data."""
        try:
            inputs = {
                "query": query,
                "table": table_data
            }
            
            result = await self.hf_client.query_model(
                self.models["table"],
                inputs
            )
            
            if "error" in result:
                return f"Table processing failed: {result['error']}"
            
            if "answer" in result:
                return result["answer"]
            
            return "I couldn't process the table query."
        
        except Exception as e:
            logger.error(f"Table processing error: {e}")
            return f"Table processing failed: {str(e)}"
    
    async def generate_response(
        self, 
        user_input: str, 
        context: Optional[str] = None,
        conversation_history: Optional[List[Dict]] = None
    ) -> str:
        """Generate a conversational response using AI capabilities."""
        try:
            # Determine the type of request and route accordingly
            user_input_lower = user_input.lower()
            
            # Check for summarization requests
            if any(word in user_input_lower for word in ["summarize", "summary", "sum up"]):
                if context:
                    return await self.summarize_text(context)
                else:
                    return "I'd be happy to summarize something for you. Please provide the text you'd like me to summarize."
            
            # Check for question-answering requests
            if any(word in user_input_lower for word in ["what", "how", "why", "when", "where", "who"]):
                if context:
                    return await self.answer_question(user_input, context)
                else:
                    # Try to answer from conversation history
                    if conversation_history:
                        recent_context = " ".join([
                            msg.get("content", "") for msg in conversation_history[-5:]
                            if msg.get("message_type") == "user"
                        ])
                        if recent_context:
                            return await self.answer_question(user_input, recent_context)
            
            # Default conversational response
            return self._generate_conversational_response(user_input, context, conversation_history)
        
        except Exception as e:
            logger.error(f"Response generation error: {e}")
            return "I apologize, but I encountered an error while processing your request. Could you please try again?"
    
    def _generate_conversational_response(
        self, 
        user_input: str, 
        context: Optional[str] = None,
        conversation_history: Optional[List[Dict]] = None
    ) -> str:
        """Generate a basic conversational response."""
        user_input_lower = user_input.lower()
        
        # Greeting responses
        if any(word in user_input_lower for word in ["hello", "hi", "hey", "good morning", "good afternoon"]):
            return "Hello! I'm NEURA, your AI assistant. How can I help you today?"
        
        # Help requests
        if any(word in user_input_lower for word in ["help", "assist", "support"]):
            return ("I'm here to help! I can assist with workflows, answer questions, "
                   "summarize documents, and remember important information. What would you like to work on?")
        
        # Workflow-related responses
        if any(word in user_input_lower for word in ["workflow", "process", "steps", "plan"]):
            return ("I'd be happy to help you create a workflow! Please tell me what goal "
                   "or process you'd like to work on, and I'll break it down into manageable steps.")
        
        # Memory-related responses
        if any(word in user_input_lower for word in ["remember", "save", "store", "note"]):
            return ("I'll remember that for you! I can store preferences, goals, important facts, "
                   "and other information to help you in future conversations.")
        
        # Default response
        return ("I understand you're saying: '" + user_input + "'. Could you provide more details "
               "about what you'd like me to help you with? I can assist with workflows, "
               "answer questions, summarize content, and much more!")
    
    async def get_model_status(self) -> Dict[str, Any]:
        """Get status of configured AI models."""
        status = {}
        
        for task, model_name in self.models.items():
            try:
                # Simple test query to check model availability
                test_result = await self.hf_client.query_model(
                    model_name,
                    "test" if task != "qa" else {"question": "test", "context": "test"}
                )
                
                status[task] = {
                    "model": model_name,
                    "available": "error" not in test_result,
                    "last_checked": datetime.utcnow().isoformat()
                }
            except Exception as e:
                status[task] = {
                    "model": model_name,
                    "available": False,
                    "error": str(e),
                    "last_checked": datetime.utcnow().isoformat()
                }
        
        return status
