# NEURA AI Assistant - MongoDB Setup Guide

This guide will help you set up NEURA with MongoDB using MongoDB Compass.

## Prerequisites

### 1. MongoDB Installation
Since you have MongoDB Compass, you likely have MongoDB installed. If not:

**Windows:**
- Download MongoDB Community Server from https://www.mongodb.com/try/download/community
- Install with default settings
- MongoDB will run as a Windows service

**Verify MongoDB is running:**
```bash
# Open Command Prompt and run:
mongosh
# If it connects, MongoDB is running
```

### 2. Python Dependencies

Install all required Python packages:

```bash
# Navigate to your NEURA directory
cd C:\Users\<USER>\Documents\Code\NEURA

# Install all dependencies
pip install -r requirements.txt
```

**Key dependencies for MongoDB:**
- `pymongo>=4.5.0` - MongoDB driver
- `motor>=3.3.0` - Async MongoDB driver
- `mongoengine>=0.27.0` - MongoDB ODM

**Voice dependencies:**
- `speechrecognition>=3.10.0` - Speech to text
- `pyttsx3>=2.90` - Text to speech
- `pyaudio>=0.2.11` - Audio processing

**AI dependencies:**
- `transformers>=4.30.0` - Hugging Face models
- `torch>=2.0.0` - PyTorch for AI models

## MongoDB Configuration

### 1. Database Setup

Using MongoDB Compass:
1. Open MongoDB Compass
2. Connect to `mongodb://localhost:27017`
3. Create a new database called `neura_db`
4. The collections will be created automatically when you first run NEURA

### 2. Environment Configuration

Your `.env` file should look like this:
```env
# Hugging Face API Configuration
HUGGINGFACE_API_TOKEN=*************************************

# Database Configuration
DATABASE_TYPE=mongodb
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=neura_db
DATABASE_ECHO=false

# Voice Configuration
VOICE_INPUT_DEVICE=default
VOICE_OUTPUT_DEVICE=default
VOICE_RATE=200
VOICE_VOLUME=0.9
VOICE_LANGUAGE=en

# AI Model Configuration
SUMMARIZATION_MODEL=facebook/bart-large-cnn
QA_MODEL=deepset/roberta-base-squad2
TABLE_MODEL=microsoft/tapex-large-finetuned-wtq

# Application Settings
DEBUG=false
LOG_LEVEL=INFO
MAX_MEMORY_ENTRIES=1000
WORKFLOW_REMINDER_HOURS=24

# GUI Settings
WINDOW_WIDTH=800
WINDOW_HEIGHT=600
THEME=dark
```

## Installation Steps

### Step 1: Install Python Dependencies

```bash
# Make sure you're in the NEURA directory
cd C:\Users\<USER>\Documents\Code\NEURA

# Install all dependencies
pip install -r requirements.txt
```

### Step 2: Verify MongoDB Connection

```bash
# Test MongoDB connection
python -c "import pymongo; client = pymongo.MongoClient('mongodb://localhost:27017'); print('MongoDB connected successfully!')"
```

### Step 3: Initialize Database

```bash
# Run the database setup script
python scripts/setup_database.py
```

### Step 4: Test Installation

```bash
# Run basic tests
python -c "from config.mongodb_models import User; print('MongoDB models loaded successfully!')"
```

### Step 5: Start NEURA

```bash
# Start NEURA in GUI mode
python main.py

# Or start in CLI mode
python main.py cli

# Or start in voice-only mode
python main.py voice
```

## Troubleshooting

### Common Issues

1. **"No module named 'pymongo'"**
   ```bash
   pip install pymongo motor mongoengine
   ```

2. **"Connection refused to MongoDB"**
   - Make sure MongoDB service is running
   - Check MongoDB Compass can connect to localhost:27017
   - Restart MongoDB service if needed

3. **"No module named 'pyaudio'"**
   ```bash
   # Windows
   pip install pyaudio
   
   # If that fails, try:
   pip install pipwin
   pipwin install pyaudio
   ```

4. **"No module named 'torch'"**
   ```bash
   # Install PyTorch
   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
   ```

5. **Voice input not working**
   - Check microphone permissions in Windows settings
   - Test microphone with Windows Voice Recorder
   - Try running: `python -c "import speech_recognition; print('Speech recognition OK')"`

### MongoDB Collections

NEURA will create these collections automatically:
- `users` - User profiles and preferences
- `memories` - Persistent memory storage
- `conversations` - Chat history
- `workflows` - Workflow definitions and progress
- `reminders` - Scheduled reminders
- `ai_model_usage` - AI usage analytics

You can view these in MongoDB Compass after running NEURA.

## Performance Tips

1. **MongoDB Indexing**: The models include proper indexes for performance
2. **Memory Management**: Set `MAX_MEMORY_ENTRIES=1000` to limit memory usage
3. **AI Models**: First-time model downloads may take a few minutes

## Security Notes

1. **Local Development**: Current setup is for local development only
2. **Production**: For production, configure MongoDB authentication
3. **API Keys**: Keep your Hugging Face token secure

## Verification Checklist

Before running NEURA, verify:
- [ ] MongoDB is running (check with MongoDB Compass)
- [ ] Python dependencies installed (`pip list | grep pymongo`)
- [ ] `.env` file configured with your API token
- [ ] Microphone and speakers connected
- [ ] Internet connection for AI models

## Running NEURA

Once everything is set up:

```bash
# GUI Mode (recommended for first-time users)
python main.py

# CLI Mode (for terminal users)
python main.py cli

# Voice Mode (hands-free interaction)
python main.py voice
```

## First-Time Usage

1. NEURA will create a demo user automatically
2. Try saying: "Hey NEURA, help me start a freelance business"
3. Or type: "start workflow" in the chat
4. Check MongoDB Compass to see data being stored

## Getting Help

If you encounter issues:
1. Check the logs in `logs/neura.log`
2. Verify MongoDB connection in Compass
3. Test individual components with the troubleshooting commands above
4. Make sure all dependencies are installed correctly

---

**Ready to start?** Run `python main.py` and enjoy your AI assistant!
