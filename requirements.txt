# NEURA AI Assistant Requirements

# Core AI and ML
transformers>=4.30.0
torch>=2.0.0
huggingface-hub>=0.16.0
datasets>=2.12.0

# Speech Recognition and Text-to-Speech
speechrecognition>=3.10.0
pyttsx3>=2.90
pyaudio>=0.2.11
sounddevice>=0.4.6
soundfile>=0.12.1

# Database and Storage
pymongo>=4.5.0
motor>=3.3.0
mongoengine>=0.27.0
sqlalchemy>=2.0.0
alembic>=1.11.0

# GUI Framework
customtkinter>=5.2.0
pillow>=10.0.0

# API and Web
requests>=2.31.0
aiohttp>=3.8.0
fastapi>=0.100.0
uvicorn>=0.22.0

# Utilities
python-dotenv>=1.0.0
pydantic>=2.0.0
pyyaml>=6.0
schedule>=1.2.0
python-dateutil>=2.8.0
pytz>=2023.3

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# Optional: For advanced features
openai>=0.27.0  # If you want to integrate with OpenAI as fallback
anthropic>=0.3.0  # If you want to integrate with <PERSON>
