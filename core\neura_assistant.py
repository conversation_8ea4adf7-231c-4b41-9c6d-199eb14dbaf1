"""
Main NEURA AI Assistant Logic
"""

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable

from .memory import MemoryManager
from .voice import VoiceManager
from .ai_engine import AI<PERSON>ngine
from .workflow import WorkflowManager
from config.settings import get_settings

logger = logging.getLogger(__name__)


class NeuraAssistant:
    """Main NEURA AI Assistant with memory, voice, and workflow capabilities."""
    
    def __init__(self):
        self.settings = get_settings()
        
        # Initialize core components
        self.memory_manager = MemoryManager()
        self.voice_manager = VoiceManager()
        self.ai_engine = AIEngine()
        self.workflow_manager = WorkflowManager()
        
        # Session management
        self.current_session_id = str(uuid.uuid4())
        self.current_user_id: Optional[int] = None
        self.is_active = False
        
        # Conversation state
        self.conversation_context = []
        self.last_interaction_time = datetime.utcnow()
        
        # Set up voice callback
        self.voice_manager.set_conversation_callback(self._handle_voice_conversation)
    
    def initialize_user(self, name: str, email: str, preferences: Dict = None) -> int:
        """Initialize or get existing user."""
        # Try to get existing user
        user = self.memory_manager.get_user_by_email(email)
        
        if not user:
            # Create new user
            user = self.memory_manager.create_user(name, email, preferences or {})
            logger.info(f"Created new user: {name} ({email})")
        else:
            logger.info(f"Found existing user: {user.name} ({user.email})")
        
        # Set current user for all managers
        self.current_user_id = user.id
        self.memory_manager.set_current_user(user.id, self.current_session_id)
        self.workflow_manager.set_current_user(user.id)
        
        return user.id
    
    def start_session(self) -> str:
        """Start a new conversation session."""
        self.current_session_id = str(uuid.uuid4())
        if self.current_user_id:
            self.memory_manager.set_current_user(self.current_user_id, self.current_session_id)
        
        self.is_active = True
        self.last_interaction_time = datetime.utcnow()
        
        logger.info(f"Started new session: {self.current_session_id}")
        return self.current_session_id
    
    def start_voice_mode(self) -> None:
        """Start voice interaction mode."""
        if not self.is_active:
            self.start_session()
        
        self.voice_manager.start_voice_interaction()
        logger.info("Voice mode activated")
    
    def stop_voice_mode(self) -> None:
        """Stop voice interaction mode."""
        self.voice_manager.stop_voice_interaction()
        logger.info("Voice mode deactivated")
    
    async def process_message(self, message: str, message_type: str = "user") -> str:
        """Process a message and generate response."""
        try:
            self.last_interaction_time = datetime.utcnow()
            
            # Store user message in memory
            if message_type == "user":
                self.memory_manager.store_conversation("user", message)
                self.conversation_context.append({"type": "user", "content": message})
            
            # Get conversation history for context
            conversation_history = self.memory_manager.get_conversation_history()
            
            # Retrieve relevant memories
            relevant_memories = self.memory_manager.search_memories(message, limit=10)
            memory_context = " ".join([mem.value for mem in relevant_memories])
            
            # Check for specific command patterns
            response = await self._handle_special_commands(message)
            
            if not response:
                # Generate AI response
                response = await self.ai_engine.generate_response(
                    message,
                    context=memory_context,
                    conversation_history=[
                        {"message_type": conv.message_type, "content": conv.content}
                        for conv in conversation_history
                    ]
                )
            
            # Store assistant response
            self.memory_manager.store_conversation("assistant", response)
            self.conversation_context.append({"type": "assistant", "content": response})
            
            # Keep conversation context manageable
            if len(self.conversation_context) > 20:
                self.conversation_context = self.conversation_context[-20:]
            
            return response
        
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return "I apologize, but I encountered an error processing your message. Could you please try again?"
    
    async def _handle_special_commands(self, message: str) -> Optional[str]:
        """Handle special commands and workflow operations."""
        message_lower = message.lower()
        
        # Workflow commands
        if "start workflow" in message_lower or "create workflow" in message_lower:
            return await self._handle_workflow_creation(message)
        
        if "workflow status" in message_lower or "workflow progress" in message_lower:
            return self._handle_workflow_status()
        
        if "complete step" in message_lower or "finish step" in message_lower:
            return self._handle_step_completion(message)
        
        if "next step" in message_lower:
            return self._handle_next_step()
        
        # Memory commands
        if "remember" in message_lower and ("that" in message_lower or "this" in message_lower):
            return self._handle_memory_storage(message)
        
        if "what do you remember" in message_lower or "my memories" in message_lower:
            return self._handle_memory_recall()
        
        # Reminder commands
        if "remind me" in message_lower:
            return self._handle_reminder_creation(message)
        
        return None
    
    async def _handle_workflow_creation(self, message: str) -> str:
        """Handle workflow creation requests."""
        message_lower = message.lower()
        
        # Check for template workflows
        templates = self.workflow_manager.list_workflow_templates()
        
        for template in templates:
            if any(keyword in message_lower for keyword in template["name"].lower().split()):
                workflow = self.workflow_manager.create_workflow_from_template(template["key"])
                if workflow:
                    return (f"Great! I've created a '{workflow.name}' workflow for you with "
                           f"{workflow.total_steps} steps. Would you like to start with the first step?")
        
        # If no template matched, offer options
        template_list = "\n".join([f"- {t['name']}: {t['description']}" for t in templates])
        return (f"I can help you create a workflow! Here are some templates I have available:\n\n"
               f"{template_list}\n\nWhich one interests you, or would you like to create a custom workflow?")
    
    def _handle_workflow_status(self) -> str:
        """Handle workflow status requests."""
        active_workflows = self.workflow_manager.get_active_workflows()
        
        if not active_workflows:
            return "You don't have any active workflows right now. Would you like to start one?"
        
        status_text = "Here are your active workflows:\n\n"
        for workflow in active_workflows:
            progress = self.workflow_manager.get_workflow_progress(workflow.id)
            status_text += (f"• {workflow.name}: {progress['completed_steps']}/{progress['total_steps']} "
                          f"steps completed ({progress['progress_percentage']}%)\n")
        
        return status_text
    
    def _handle_step_completion(self, message: str) -> str:
        """Handle step completion requests."""
        active_workflows = self.workflow_manager.get_active_workflows()
        
        if not active_workflows:
            return "You don't have any active workflows to update."
        
        # For simplicity, complete the current step of the first active workflow
        workflow = active_workflows[0]
        if self.workflow_manager.complete_step(workflow.id, workflow.current_step):
            next_suggestion = self.workflow_manager.get_next_step_suggestion(workflow.id)
            return f"Great job! Step {workflow.current_step} completed. {next_suggestion}"
        else:
            return "I couldn't complete that step. Please check your workflow status."
    
    def _handle_next_step(self) -> str:
        """Handle next step requests."""
        active_workflows = self.workflow_manager.get_active_workflows()
        
        if not active_workflows:
            return "You don't have any active workflows. Would you like to start one?"
        
        suggestions = []
        for workflow in active_workflows:
            suggestion = self.workflow_manager.get_next_step_suggestion(workflow.id)
            if suggestion:
                suggestions.append(f"{workflow.name}: {suggestion}")
        
        if suggestions:
            return "Here's what you can work on next:\n\n" + "\n".join(suggestions)
        else:
            return "All your workflows are up to date! Great work!"
    
    def _handle_memory_storage(self, message: str) -> str:
        """Handle memory storage requests."""
        # Extract what to remember (simple implementation)
        if "remember that" in message.lower():
            content = message.lower().split("remember that", 1)[1].strip()
        elif "remember this" in message.lower():
            content = message.lower().split("remember this", 1)[1].strip()
        else:
            content = message
        
        if content:
            self.memory_manager.store_memory("user_note", "manual_entry", content, importance=0.8)
            return f"I'll remember that: {content}"
        else:
            return "What would you like me to remember?"
    
    def _handle_memory_recall(self) -> str:
        """Handle memory recall requests."""
        memories = self.memory_manager.retrieve_memory(limit=10)
        
        if not memories:
            return "I don't have any specific memories stored yet. As we interact more, I'll remember important information about your preferences and goals."
        
        memory_text = "Here's what I remember about you:\n\n"
        for memory in memories[:5]:  # Show top 5
            memory_text += f"• {memory.key}: {memory.value}\n"
        
        return memory_text
    
    def _handle_reminder_creation(self, message: str) -> str:
        """Handle reminder creation (basic implementation)."""
        # This is a simplified implementation
        # In a full version, you'd parse the time and create actual reminders
        return ("I'd love to set up reminders for you! This feature is coming soon. "
               "For now, I can help you create workflows with built-in progress tracking.")
    
    def _handle_voice_conversation(self, voice_input: str) -> str:
        """Handle voice input and return response."""
        try:
            # Process the voice input asynchronously
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            response = loop.run_until_complete(self.process_message(voice_input))
            loop.close()
            return response
        except Exception as e:
            logger.error(f"Voice conversation error: {e}")
            return "I'm sorry, I had trouble processing that. Could you try again?"
    
    def speak(self, text: str, immediate: bool = False) -> None:
        """Make NEURA speak the given text."""
        self.voice_manager.speak(text, immediate)
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get summary of current session."""
        return {
            "session_id": self.current_session_id,
            "user_id": self.current_user_id,
            "is_active": self.is_active,
            "last_interaction": self.last_interaction_time.isoformat(),
            "conversation_length": len(self.conversation_context),
            "memory_stats": self.memory_manager.get_memory_stats() if self.current_user_id else {}
        }
    
    def shutdown(self) -> None:
        """Shutdown NEURA assistant."""
        self.stop_voice_mode()
        self.voice_manager.shutdown()
        self.is_active = False
        logger.info("NEURA assistant shutdown complete")
