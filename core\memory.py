"""
Memory Management System for NEURA AI Assistant
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from config.models import (
    User, Memory, Conversation, Workflow, WorkflowStep, 
    Reminder, get_db, SessionLocal
)
from config.settings import get_settings

logger = logging.getLogger(__name__)


class MemoryManager:
    """Manages persistent memory for NEURA AI Assistant."""
    
    def __init__(self):
        self.settings = get_settings()
        self.current_user_id: Optional[int] = None
        self.session_id: Optional[str] = None
    
    def set_current_user(self, user_id: int, session_id: str) -> None:
        """Set the current user and session."""
        self.current_user_id = user_id
        self.session_id = session_id
    
    def create_user(self, name: str, email: str, preferences: Dict = None) -> User:
        """Create a new user."""
        with SessionLocal() as db:
            user = User(
                name=name,
                email=email,
                preferences=preferences or {}
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            return user
    
    def get_user(self, user_id: int) -> Optional[User]:
        """Get user by ID."""
        with SessionLocal() as db:
            return db.query(User).filter(User.id == user_id).first()
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        with SessionLocal() as db:
            return db.query(User).filter(User.email == email).first()
    
    def store_memory(
        self, 
        memory_type: str, 
        key: str, 
        value: str, 
        importance: float = 1.0,
        metadata: Dict = None,
        expires_in_days: Optional[int] = None
    ) -> Memory:
        """Store a memory for the current user."""
        if not self.current_user_id:
            raise ValueError("No current user set")
        
        expires_at = None
        if expires_in_days:
            expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
        
        with SessionLocal() as db:
            # Check if memory already exists and update it
            existing = db.query(Memory).filter(
                and_(
                    Memory.user_id == self.current_user_id,
                    Memory.memory_type == memory_type,
                    Memory.key == key
                )
            ).first()
            
            if existing:
                existing.value = value
                existing.importance = importance
                existing.metadata = metadata or {}
                existing.updated_at = datetime.utcnow()
                existing.expires_at = expires_at
                memory = existing
            else:
                memory = Memory(
                    user_id=self.current_user_id,
                    memory_type=memory_type,
                    key=key,
                    value=value,
                    importance=importance,
                    metadata=metadata or {},
                    expires_at=expires_at
                )
                db.add(memory)
            
            db.commit()
            db.refresh(memory)
            return memory
    
    def retrieve_memory(
        self, 
        memory_type: Optional[str] = None, 
        key: Optional[str] = None,
        limit: int = 100
    ) -> List[Memory]:
        """Retrieve memories for the current user."""
        if not self.current_user_id:
            return []
        
        with SessionLocal() as db:
            query = db.query(Memory).filter(
                and_(
                    Memory.user_id == self.current_user_id,
                    or_(
                        Memory.expires_at.is_(None),
                        Memory.expires_at > datetime.utcnow()
                    )
                )
            )
            
            if memory_type:
                query = query.filter(Memory.memory_type == memory_type)
            
            if key:
                query = query.filter(Memory.key == key)
            
            return query.order_by(desc(Memory.importance), desc(Memory.updated_at)).limit(limit).all()
    
    def search_memories(self, search_term: str, limit: int = 50) -> List[Memory]:
        """Search memories by content."""
        if not self.current_user_id:
            return []
        
        with SessionLocal() as db:
            return db.query(Memory).filter(
                and_(
                    Memory.user_id == self.current_user_id,
                    or_(
                        Memory.key.contains(search_term),
                        Memory.value.contains(search_term)
                    ),
                    or_(
                        Memory.expires_at.is_(None),
                        Memory.expires_at > datetime.utcnow()
                    )
                )
            ).order_by(desc(Memory.importance)).limit(limit).all()
    
    def store_conversation(
        self, 
        message_type: str, 
        content: str, 
        metadata: Dict = None
    ) -> Conversation:
        """Store a conversation message."""
        if not self.current_user_id or not self.session_id:
            raise ValueError("No current user or session set")
        
        with SessionLocal() as db:
            conversation = Conversation(
                user_id=self.current_user_id,
                session_id=self.session_id,
                message_type=message_type,
                content=content,
                metadata=metadata or {}
            )
            db.add(conversation)
            db.commit()
            db.refresh(conversation)
            return conversation
    
    def get_conversation_history(
        self, 
        session_id: Optional[str] = None, 
        limit: int = 50
    ) -> List[Conversation]:
        """Get conversation history."""
        if not self.current_user_id:
            return []
        
        target_session = session_id or self.session_id
        if not target_session:
            return []
        
        with SessionLocal() as db:
            return db.query(Conversation).filter(
                and_(
                    Conversation.user_id == self.current_user_id,
                    Conversation.session_id == target_session
                )
            ).order_by(Conversation.timestamp).limit(limit).all()
    
    def get_recent_conversations(self, days: int = 7, limit: int = 100) -> List[Conversation]:
        """Get recent conversations across all sessions."""
        if not self.current_user_id:
            return []
        
        since_date = datetime.utcnow() - timedelta(days=days)
        
        with SessionLocal() as db:
            return db.query(Conversation).filter(
                and_(
                    Conversation.user_id == self.current_user_id,
                    Conversation.timestamp >= since_date
                )
            ).order_by(desc(Conversation.timestamp)).limit(limit).all()
    
    def cleanup_expired_memories(self) -> int:
        """Remove expired memories and return count of removed items."""
        with SessionLocal() as db:
            expired_count = db.query(Memory).filter(
                Memory.expires_at <= datetime.utcnow()
            ).count()
            
            db.query(Memory).filter(
                Memory.expires_at <= datetime.utcnow()
            ).delete()
            
            db.commit()
            return expired_count
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        if not self.current_user_id:
            return {}
        
        with SessionLocal() as db:
            total_memories = db.query(Memory).filter(
                Memory.user_id == self.current_user_id
            ).count()
            
            memory_types = db.query(Memory.memory_type).filter(
                Memory.user_id == self.current_user_id
            ).distinct().all()
            
            recent_conversations = db.query(Conversation).filter(
                and_(
                    Conversation.user_id == self.current_user_id,
                    Conversation.timestamp >= datetime.utcnow() - timedelta(days=7)
                )
            ).count()
            
            return {
                "total_memories": total_memories,
                "memory_types": [mt[0] for mt in memory_types],
                "recent_conversations": recent_conversations,
                "current_session": self.session_id
            }
