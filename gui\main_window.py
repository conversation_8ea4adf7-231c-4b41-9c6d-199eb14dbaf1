"""
Main GUI Window for NEURA AI Assistant
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, simpledialog
import threading
import async<PERSON>
from datetime import datetime
from typing import Optional

try:
    import customtkinter as ctk
    CTK_AVAILABLE = True
except ImportError:
    CTK_AVAILABLE = False

from core.neura_assistant import NeuraAssistant
from config.settings import get_settings

class NeuraGUI:
    """Main GUI application for NEURA AI Assistant."""
    
    def __init__(self):
        self.settings = get_settings()
        self.neura = NeuraAssistant()
        
        # Initialize GUI
        if CTK_AVAILABLE:
            ctk.set_appearance_mode(self.settings.theme)
            ctk.set_default_color_theme("blue")
            self.root = ctk.CTk()
        else:
            self.root = tk.Tk()
        
        self.setup_window()
        self.create_widgets()
        self.setup_user()
        
        # Voice mode state
        self.voice_mode_active = False
        
    def setup_window(self):
        """Configure the main window."""
        self.root.title("NEURA - AI Assistant")
        self.root.geometry(f"{self.settings.window_width}x{self.settings.window_height}")
        
        # Center window on screen
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.settings.window_width // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.settings.window_height // 2)
        self.root.geometry(f"{self.settings.window_width}x{self.settings.window_height}+{x}+{y}")
        
        # Configure grid weights
        self.root.grid_rowconfigure(1, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
    
    def create_widgets(self):
        """Create and arrange GUI widgets."""
        # Header frame
        header_frame = self.create_frame(self.root)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=5)
        
        # Title
        title_label = self.create_label(header_frame, "NEURA AI Assistant", font_size=20)
        title_label.grid(row=0, column=0, sticky="w")
        
        # Status label
        self.status_label = self.create_label(header_frame, "Ready", font_size=12)
        self.status_label.grid(row=0, column=1, sticky="e")
        header_frame.grid_columnconfigure(1, weight=1)
        
        # Main content frame
        main_frame = self.create_frame(self.root)
        main_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=5)
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_columnconfigure(0, weight=2)
        main_frame.grid_columnconfigure(1, weight=1)
        
        # Chat area
        chat_frame = self.create_frame(main_frame)
        chat_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        chat_frame.grid_rowconfigure(0, weight=1)
        chat_frame.grid_columnconfigure(0, weight=1)
        
        # Chat display
        if CTK_AVAILABLE:
            self.chat_display = ctk.CTkTextbox(chat_frame, wrap="word", state="disabled")
        else:
            self.chat_display = scrolledtext.ScrolledText(chat_frame, wrap=tk.WORD, state=tk.DISABLED)
        self.chat_display.grid(row=0, column=0, sticky="nsew", pady=(0, 10))
        
        # Input frame
        input_frame = self.create_frame(chat_frame)
        input_frame.grid(row=1, column=0, sticky="ew")
        input_frame.grid_columnconfigure(0, weight=1)
        
        # Text input
        if CTK_AVAILABLE:
            self.text_input = ctk.CTkEntry(input_frame, placeholder_text="Type your message here...")
        else:
            self.text_input = tk.Entry(input_frame)
        self.text_input.grid(row=0, column=0, sticky="ew", padx=(0, 5))
        self.text_input.bind("<Return>", self.send_message)
        
        # Send button
        self.send_button = self.create_button(input_frame, "Send", self.send_message)
        self.send_button.grid(row=0, column=1)
        
        # Side panel
        side_frame = self.create_frame(main_frame)
        side_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        
        # Voice controls
        voice_frame = self.create_frame(side_frame)
        voice_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        voice_label = self.create_label(voice_frame, "Voice Controls", font_size=14)
        voice_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
        
        self.voice_button = self.create_button(voice_frame, "Start Voice Mode", self.toggle_voice_mode)
        self.voice_button.grid(row=1, column=0, sticky="ew")
        
        self.listen_button = self.create_button(voice_frame, "Listen Once", self.listen_once)
        self.listen_button.grid(row=2, column=0, sticky="ew", pady=(5, 0))
        
        # Workflow controls
        workflow_frame = self.create_frame(side_frame)
        workflow_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        
        workflow_label = self.create_label(workflow_frame, "Workflows", font_size=14)
        workflow_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
        
        self.workflow_button = self.create_button(workflow_frame, "View Workflows", self.show_workflows)
        self.workflow_button.grid(row=1, column=0, sticky="ew")
        
        self.new_workflow_button = self.create_button(workflow_frame, "New Workflow", self.create_workflow)
        self.new_workflow_button.grid(row=2, column=0, sticky="ew", pady=(5, 0))
        
        # Memory controls
        memory_frame = self.create_frame(side_frame)
        memory_frame.grid(row=2, column=0, sticky="ew", pady=(0, 10))
        
        memory_label = self.create_label(memory_frame, "Memory", font_size=14)
        memory_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
        
        self.memory_button = self.create_button(memory_frame, "View Memories", self.show_memories)
        self.memory_button.grid(row=1, column=0, sticky="ew")
        
        # Settings
        settings_frame = self.create_frame(side_frame)
        settings_frame.grid(row=3, column=0, sticky="ew")
        
        settings_label = self.create_label(settings_frame, "Settings", font_size=14)
        settings_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
        
        self.settings_button = self.create_button(settings_frame, "Preferences", self.show_settings)
        self.settings_button.grid(row=1, column=0, sticky="ew")
        
        # Configure side frame rows
        side_frame.grid_rowconfigure(4, weight=1)  # Push everything to top
        
        # Bottom frame
        bottom_frame = self.create_frame(self.root)
        bottom_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=5)
        
        # Session info
        self.session_label = self.create_label(bottom_frame, "Session: Not started", font_size=10)
        self.session_label.grid(row=0, column=0, sticky="w")
    
    def create_frame(self, parent):
        """Create a frame widget."""
        if CTK_AVAILABLE:
            return ctk.CTkFrame(parent)
        else:
            return ttk.Frame(parent)
    
    def create_label(self, parent, text, font_size=12):
        """Create a label widget."""
        if CTK_AVAILABLE:
            return ctk.CTkLabel(parent, text=text, font=("Arial", font_size))
        else:
            return ttk.Label(parent, text=text)
    
    def create_button(self, parent, text, command):
        """Create a button widget."""
        if CTK_AVAILABLE:
            return ctk.CTkButton(parent, text=text, command=command)
        else:
            return ttk.Button(parent, text=text, command=command)
    
    def setup_user(self):
        """Setup user for the session."""
        # For demo purposes, use default user
        # In production, you'd have a proper login system
        try:
            user_id = self.neura.initialize_user("Demo User", "<EMAIL>")
            session_id = self.neura.start_session()
            self.update_status("Ready - User initialized")
            self.session_label.configure(text=f"Session: {session_id[:8]}...")
            
            # Welcome message
            self.add_message("NEURA", "Hello! I'm NEURA, your AI assistant. I can help with workflows, answer questions, and remember important information. How can I assist you today?")
        except Exception as e:
            self.update_status(f"Error: {str(e)}")
            messagebox.showerror("Initialization Error", f"Failed to initialize NEURA: {str(e)}")
    
    def add_message(self, sender, message):
        """Add a message to the chat display."""
        timestamp = datetime.now().strftime("%H:%M")
        
        if CTK_AVAILABLE:
            self.chat_display.configure(state="normal")
            self.chat_display.insert("end", f"[{timestamp}] {sender}: {message}\n\n")
            self.chat_display.configure(state="disabled")
            self.chat_display.see("end")
        else:
            self.chat_display.config(state=tk.NORMAL)
            self.chat_display.insert(tk.END, f"[{timestamp}] {sender}: {message}\n\n")
            self.chat_display.config(state=tk.DISABLED)
            self.chat_display.see(tk.END)
    
    def update_status(self, status):
        """Update the status label."""
        if CTK_AVAILABLE:
            self.status_label.configure(text=status)
        else:
            self.status_label.config(text=status)
    
    def send_message(self, event=None):
        """Send a message to NEURA."""
        message = self.text_input.get().strip()
        if not message:
            return
        
        # Clear input
        self.text_input.delete(0, tk.END)
        
        # Add user message to chat
        self.add_message("You", message)
        
        # Process message in background thread
        threading.Thread(target=self._process_message_async, args=(message,), daemon=True).start()
    
    def _process_message_async(self, message):
        """Process message asynchronously."""
        try:
            self.update_status("Processing...")
            
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Process message
            response = loop.run_until_complete(self.neura.process_message(message))
            
            # Update GUI in main thread
            self.root.after(0, lambda: self.add_message("NEURA", response))
            self.root.after(0, lambda: self.update_status("Ready"))
            
            loop.close()
        except Exception as e:
            error_msg = f"Error processing message: {str(e)}"
            self.root.after(0, lambda: self.add_message("NEURA", error_msg))
            self.root.after(0, lambda: self.update_status("Error"))
    
    def toggle_voice_mode(self):
        """Toggle voice interaction mode."""
        if not self.voice_mode_active:
            try:
                self.neura.start_voice_mode()
                self.voice_mode_active = True
                self.voice_button.configure(text="Stop Voice Mode")
                self.update_status("Voice mode active - Say 'Hey NEURA' to interact")
                self.add_message("NEURA", "Voice mode activated! Say 'Hey NEURA' followed by your request.")
            except Exception as e:
                messagebox.showerror("Voice Error", f"Failed to start voice mode: {str(e)}")
        else:
            self.neura.stop_voice_mode()
            self.voice_mode_active = False
            self.voice_button.configure(text="Start Voice Mode")
            self.update_status("Voice mode stopped")
            self.add_message("NEURA", "Voice mode deactivated.")
    
    def listen_once(self):
        """Listen for speech once."""
        threading.Thread(target=self._listen_once_async, daemon=True).start()
    
    def _listen_once_async(self):
        """Listen for speech once asynchronously."""
        try:
            self.root.after(0, lambda: self.update_status("Listening..."))
            
            text = self.neura.voice_manager.listen_once(timeout=5.0)
            
            if text:
                self.root.after(0, lambda: self.add_message("You (Voice)", text))
                
                # Process the voice input
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                response = loop.run_until_complete(self.neura.process_message(text))
                loop.close()
                
                self.root.after(0, lambda: self.add_message("NEURA", response))
                self.neura.speak(response)
            else:
                self.root.after(0, lambda: self.add_message("NEURA", "I didn't hear anything. Please try again."))
            
            self.root.after(0, lambda: self.update_status("Ready"))
        except Exception as e:
            error_msg = f"Voice input error: {str(e)}"
            self.root.after(0, lambda: self.add_message("NEURA", error_msg))
            self.root.after(0, lambda: self.update_status("Ready"))
    
    def show_workflows(self):
        """Show workflow management window."""
        messagebox.showinfo("Workflows", "Workflow management window coming soon!\nFor now, you can create workflows by typing 'start workflow' in the chat.")
    
    def create_workflow(self):
        """Create a new workflow."""
        self.text_input.delete(0, tk.END)
        self.text_input.insert(0, "start workflow")
        self.send_message()
    
    def show_memories(self):
        """Show memory management window."""
        self.text_input.delete(0, tk.END)
        self.text_input.insert(0, "what do you remember about me")
        self.send_message()
    
    def show_settings(self):
        """Show settings window."""
        messagebox.showinfo("Settings", "Settings window coming soon!\nYou can modify settings in the .env file for now.")
    
    def run(self):
        """Start the GUI application."""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()
    
    def on_closing(self):
        """Handle application closing."""
        if self.voice_mode_active:
            self.neura.stop_voice_mode()
        
        self.neura.shutdown()
        self.root.destroy()


def main():
    """Main entry point for the GUI application."""
    app = NeuraGUI()
    app.run()


if __name__ == "__main__":
    main()
