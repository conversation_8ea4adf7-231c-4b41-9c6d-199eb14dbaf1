"""
Installation Script for NEURA AI Assistant
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True


def install_dependencies():
    """Install Python dependencies."""
    print("📦 Installing Python dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install requirements
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_environment():
    """Setup environment configuration."""
    print("⚙️ Setting up environment...")
    
    env_file = Path(".env")
    example_file = Path(".env.example")
    
    if not env_file.exists() and example_file.exists():
        import shutil
        shutil.copy(example_file, env_file)
        print("✅ Created .env file from template")
        print("📝 Please edit .env file with your API keys!")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("❌ .env.example file not found!")
        return False
    
    return True


def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = ["logs", "data", "models", "temp"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ Directories created")
    return True


def setup_database():
    """Setup database."""
    print("🗄️ Setting up database...")
    
    try:
        # Run database setup script
        subprocess.check_call([sys.executable, "scripts/setup_database.py"])
        print("✅ Database setup completed!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Database setup failed: {e}")
        return False


def install_system_dependencies():
    """Install system-level dependencies."""
    system = platform.system().lower()
    
    print(f"🖥️ Detected system: {system}")
    
    if system == "windows":
        print("📢 Windows detected:")
        print("   - Make sure you have a microphone and speakers connected")
        print("   - Windows Speech Recognition should be available")
        
    elif system == "darwin":  # macOS
        print("🍎 macOS detected:")
        print("   - Make sure you have microphone permissions enabled")
        print("   - You may need to install portaudio: brew install portaudio")
        
    elif system == "linux":
        print("🐧 Linux detected:")
        print("   - You may need to install additional packages:")
        print("   - Ubuntu/Debian: sudo apt-get install python3-pyaudio portaudio19-dev")
        print("   - Fedora: sudo dnf install python3-pyaudio portaudio-devel")
        
    return True


def test_installation():
    """Test if installation was successful."""
    print("🧪 Testing installation...")
    
    try:
        # Test imports
        import transformers
        import torch
        import speechrecognition
        import pyttsx3
        import sqlalchemy
        
        print("✅ All core dependencies imported successfully!")
        
        # Test database connection
        from config.models import engine
        from sqlalchemy import text
        
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        
        print("✅ Database connection successful!")
        return True
        
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        return False


def main():
    """Main installation function."""
    print("🚀 NEURA AI Assistant Installation")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install system dependencies info
    install_system_dependencies()
    
    # Install Python dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        sys.exit(1)
    
    # Setup database
    if not setup_database():
        sys.exit(1)
    
    # Test installation
    if not test_installation():
        print("⚠️ Installation completed with warnings. Some features may not work.")
    else:
        print("🎉 Installation completed successfully!")
    
    print("\n📋 Next steps:")
    print("1. Edit .env file with your Hugging Face API token")
    print("2. Run: python main.py")
    print("3. Enjoy using NEURA!")


if __name__ == "__main__":
    main()
