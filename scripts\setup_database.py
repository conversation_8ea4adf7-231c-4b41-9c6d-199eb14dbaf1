"""
Database Setup Script for NEURA AI Assistant
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.models import create_tables, engine, SessionLocal
from config.models import User, Memory, Workflow, WorkflowStep
from config.settings import get_settings
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def setup_database():
    """Setup and initialize the database."""
    try:
        logger.info("Creating database tables...")
        create_tables(engine)
        logger.info("Database tables created successfully!")
        
        # Create a default demo user
        with SessionLocal() as db:
            # Check if demo user already exists
            existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
            
            if not existing_user:
                demo_user = User(
                    name="Demo User",
                    email="<EMAIL>",
                    preferences={
                        "voice_enabled": True,
                        "theme": "dark",
                        "language": "en"
                    }
                )
                db.add(demo_user)
                db.commit()
                logger.info("Created demo user account")
            else:
                logger.info("Demo user already exists")
        
        logger.info("Database setup completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        return False


def reset_database():
    """Reset the database (drop and recreate all tables)."""
    try:
        logger.warning("Resetting database - all data will be lost!")
        
        # Drop all tables
        from config.models import Base
        Base.metadata.drop_all(bind=engine)
        logger.info("Dropped all tables")
        
        # Recreate tables
        setup_database()
        logger.info("Database reset completed!")
        return True
        
    except Exception as e:
        logger.error(f"Database reset failed: {e}")
        return False


def main():
    """Main function for database setup."""
    if len(sys.argv) > 1 and sys.argv[1] == "--reset":
        reset_database()
    else:
        setup_database()


if __name__ == "__main__":
    main()
