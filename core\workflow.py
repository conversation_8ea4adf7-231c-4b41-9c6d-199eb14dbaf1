"""
Workflow Management System for NEURA AI Assistant
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from config.models import (
    Workflow, WorkflowStep, Reminder, User, SessionLocal
)
from config.settings import get_settings

logger = logging.getLogger(__name__)


class WorkflowManager:
    """Manages workflows and step-by-step processes for users."""
    
    def __init__(self):
        self.settings = get_settings()
        self.current_user_id: Optional[int] = None
        
        # Predefined workflow templates
        self.workflow_templates = {
            "freelance_business": {
                "name": "Start a Freelance Business",
                "description": "Complete guide to starting your freelance business",
                "steps": [
                    {"title": "Choose Your Niche", "description": "Identify your skills and target market"},
                    {"title": "Build a Portfolio", "description": "Create samples of your best work"},
                    {"title": "Create Personal Brand", "description": "Design logo, website, and social media presence"},
                    {"title": "Set Up Business Structure", "description": "Register business, get licenses, set up accounting"},
                    {"title": "Establish Payment Systems", "description": "Set up invoicing and payment processing"},
                    {"title": "Find Your First Clients", "description": "Network, apply to jobs, and market your services"},
                    {"title": "Deliver and Scale", "description": "Complete projects and grow your client base"}
                ]
            },
            "uk_visa_application": {
                "name": "UK Visa Application Process",
                "description": "Step-by-step guide for UK visa application",
                "steps": [
                    {"title": "Determine Visa Type", "description": "Research and select the appropriate visa category"},
                    {"title": "Gather Required Documents", "description": "Collect passport, photos, financial documents, etc."},
                    {"title": "Complete Online Application", "description": "Fill out the visa application form online"},
                    {"title": "Pay Application Fee", "description": "Submit payment for visa processing"},
                    {"title": "Book Biometric Appointment", "description": "Schedule appointment at visa application center"},
                    {"title": "Attend Biometric Appointment", "description": "Provide fingerprints and photograph"},
                    {"title": "Wait for Decision", "description": "Track application status and wait for response"},
                    {"title": "Collect Passport", "description": "Retrieve passport with visa decision"}
                ]
            },
            "website_creation": {
                "name": "Create a Professional Website",
                "description": "Build a website from planning to launch",
                "steps": [
                    {"title": "Define Website Goals", "description": "Determine purpose, audience, and objectives"},
                    {"title": "Plan Site Structure", "description": "Create sitemap and wireframes"},
                    {"title": "Choose Platform", "description": "Select CMS, hosting, and domain name"},
                    {"title": "Design Interface", "description": "Create visual design and user experience"},
                    {"title": "Develop Content", "description": "Write copy, gather images, and create media"},
                    {"title": "Build and Test", "description": "Code the site and test functionality"},
                    {"title": "Launch and Optimize", "description": "Go live and monitor performance"}
                ]
            },
            "tax_filing": {
                "name": "File Annual Tax Return",
                "description": "Complete tax filing process",
                "steps": [
                    {"title": "Gather Tax Documents", "description": "Collect W-2s, 1099s, receipts, and records"},
                    {"title": "Choose Filing Method", "description": "Decide on software, professional, or paper filing"},
                    {"title": "Review Previous Year", "description": "Check last year's return for reference"},
                    {"title": "Complete Tax Forms", "description": "Fill out all required tax forms and schedules"},
                    {"title": "Review and Double-Check", "description": "Verify all information and calculations"},
                    {"title": "Submit Return", "description": "File electronically or mail paper return"},
                    {"title": "Track Refund/Payment", "description": "Monitor refund status or make payment if owed"}
                ]
            }
        }
    
    def set_current_user(self, user_id: int) -> None:
        """Set the current user for workflow operations."""
        self.current_user_id = user_id
    
    def create_workflow_from_template(self, template_name: str) -> Optional[Workflow]:
        """Create a new workflow from a predefined template."""
        if not self.current_user_id:
            raise ValueError("No current user set")
        
        if template_name not in self.workflow_templates:
            return None
        
        template = self.workflow_templates[template_name]
        
        with SessionLocal() as db:
            workflow = Workflow(
                user_id=self.current_user_id,
                name=template["name"],
                description=template["description"],
                total_steps=len(template["steps"]),
                steps_data=template["steps"],
                status="active"
            )
            
            db.add(workflow)
            db.commit()
            db.refresh(workflow)
            
            # Create individual workflow steps
            for i, step_data in enumerate(template["steps"]):
                step = WorkflowStep(
                    workflow_id=workflow.id,
                    step_number=i + 1,
                    title=step_data["title"],
                    description=step_data["description"],
                    status="pending"
                )
                db.add(step)
            
            db.commit()
            logger.info(f"Created workflow '{template['name']}' for user {self.current_user_id}")
            return workflow
    
    def create_custom_workflow(
        self, 
        name: str, 
        description: str, 
        steps: List[Dict[str, str]]
    ) -> Workflow:
        """Create a custom workflow with user-defined steps."""
        if not self.current_user_id:
            raise ValueError("No current user set")
        
        with SessionLocal() as db:
            workflow = Workflow(
                user_id=self.current_user_id,
                name=name,
                description=description,
                total_steps=len(steps),
                steps_data=steps,
                status="active"
            )
            
            db.add(workflow)
            db.commit()
            db.refresh(workflow)
            
            # Create individual workflow steps
            for i, step_data in enumerate(steps):
                step = WorkflowStep(
                    workflow_id=workflow.id,
                    step_number=i + 1,
                    title=step_data.get("title", f"Step {i + 1}"),
                    description=step_data.get("description", ""),
                    status="pending"
                )
                db.add(step)
            
            db.commit()
            logger.info(f"Created custom workflow '{name}' for user {self.current_user_id}")
            return workflow
    
    def get_active_workflows(self) -> List[Workflow]:
        """Get all active workflows for the current user."""
        if not self.current_user_id:
            return []
        
        with SessionLocal() as db:
            return db.query(Workflow).filter(
                and_(
                    Workflow.user_id == self.current_user_id,
                    Workflow.status == "active"
                )
            ).order_by(desc(Workflow.updated_at)).all()
    
    def get_workflow(self, workflow_id: int) -> Optional[Workflow]:
        """Get a specific workflow by ID."""
        if not self.current_user_id:
            return None
        
        with SessionLocal() as db:
            return db.query(Workflow).filter(
                and_(
                    Workflow.id == workflow_id,
                    Workflow.user_id == self.current_user_id
                )
            ).first()
    
    def get_workflow_steps(self, workflow_id: int) -> List[WorkflowStep]:
        """Get all steps for a workflow."""
        with SessionLocal() as db:
            return db.query(WorkflowStep).filter(
                WorkflowStep.workflow_id == workflow_id
            ).order_by(WorkflowStep.step_number).all()
    
    def start_step(self, workflow_id: int, step_number: int) -> bool:
        """Mark a workflow step as started."""
        with SessionLocal() as db:
            step = db.query(WorkflowStep).filter(
                and_(
                    WorkflowStep.workflow_id == workflow_id,
                    WorkflowStep.step_number == step_number
                )
            ).first()
            
            if step:
                step.status = "in_progress"
                step.started_at = datetime.utcnow()
                
                # Update workflow current step
                workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
                if workflow:
                    workflow.current_step = step_number
                    workflow.updated_at = datetime.utcnow()
                
                db.commit()
                logger.info(f"Started step {step_number} of workflow {workflow_id}")
                return True
            
            return False
    
    def complete_step(self, workflow_id: int, step_number: int) -> bool:
        """Mark a workflow step as completed."""
        with SessionLocal() as db:
            step = db.query(WorkflowStep).filter(
                and_(
                    WorkflowStep.workflow_id == workflow_id,
                    WorkflowStep.step_number == step_number
                )
            ).first()
            
            if step:
                step.status = "complete"
                step.completed_at = datetime.utcnow()
                
                # Update workflow progress
                workflow = db.query(Workflow).filter(Workflow.id == workflow_id).first()
                if workflow:
                    completed_steps = db.query(WorkflowStep).filter(
                        and_(
                            WorkflowStep.workflow_id == workflow_id,
                            WorkflowStep.status == "complete"
                        )
                    ).count()
                    
                    if completed_steps >= workflow.total_steps:
                        workflow.status = "completed"
                        workflow.completed_at = datetime.utcnow()
                    else:
                        workflow.current_step = step_number + 1
                    
                    workflow.updated_at = datetime.utcnow()
                
                db.commit()
                logger.info(f"Completed step {step_number} of workflow {workflow_id}")
                return True
            
            return False
    
    def pause_workflow(self, workflow_id: int) -> bool:
        """Pause a workflow."""
        with SessionLocal() as db:
            workflow = db.query(Workflow).filter(
                and_(
                    Workflow.id == workflow_id,
                    Workflow.user_id == self.current_user_id
                )
            ).first()
            
            if workflow:
                workflow.status = "paused"
                workflow.updated_at = datetime.utcnow()
                db.commit()
                logger.info(f"Paused workflow {workflow_id}")
                return True
            
            return False
    
    def resume_workflow(self, workflow_id: int) -> bool:
        """Resume a paused workflow."""
        with SessionLocal() as db:
            workflow = db.query(Workflow).filter(
                and_(
                    Workflow.id == workflow_id,
                    Workflow.user_id == self.current_user_id
                )
            ).first()
            
            if workflow and workflow.status == "paused":
                workflow.status = "active"
                workflow.updated_at = datetime.utcnow()
                db.commit()
                logger.info(f"Resumed workflow {workflow_id}")
                return True
            
            return False
    
    def get_workflow_progress(self, workflow_id: int) -> Dict[str, Any]:
        """Get detailed progress information for a workflow."""
        workflow = self.get_workflow(workflow_id)
        if not workflow:
            return {}
        
        steps = self.get_workflow_steps(workflow_id)
        
        completed_steps = sum(1 for step in steps if step.status == "complete")
        in_progress_steps = sum(1 for step in steps if step.status == "in_progress")
        
        progress_percentage = (completed_steps / workflow.total_steps) * 100 if workflow.total_steps > 0 else 0
        
        return {
            "workflow_id": workflow.id,
            "name": workflow.name,
            "status": workflow.status,
            "current_step": workflow.current_step,
            "total_steps": workflow.total_steps,
            "completed_steps": completed_steps,
            "in_progress_steps": in_progress_steps,
            "progress_percentage": round(progress_percentage, 1),
            "created_at": workflow.created_at.isoformat(),
            "updated_at": workflow.updated_at.isoformat(),
            "steps": [
                {
                    "step_number": step.step_number,
                    "title": step.title,
                    "description": step.description,
                    "status": step.status,
                    "started_at": step.started_at.isoformat() if step.started_at else None,
                    "completed_at": step.completed_at.isoformat() if step.completed_at else None
                }
                for step in steps
            ]
        }
    
    def get_next_step_suggestion(self, workflow_id: int) -> Optional[str]:
        """Get suggestion for the next step in a workflow."""
        workflow = self.get_workflow(workflow_id)
        if not workflow or workflow.status != "active":
            return None
        
        steps = self.get_workflow_steps(workflow_id)
        
        # Find the next pending step
        for step in steps:
            if step.status == "pending":
                return f"Ready to start: {step.title} - {step.description}"
        
        # Check if there's an in-progress step
        for step in steps:
            if step.status == "in_progress":
                return f"Continue working on: {step.title} - {step.description}"
        
        # All steps completed
        return "Congratulations! You've completed all steps in this workflow."
    
    def list_workflow_templates(self) -> List[Dict[str, str]]:
        """Get list of available workflow templates."""
        return [
            {
                "key": key,
                "name": template["name"],
                "description": template["description"],
                "steps_count": len(template["steps"])
            }
            for key, template in self.workflow_templates.items()
        ]
