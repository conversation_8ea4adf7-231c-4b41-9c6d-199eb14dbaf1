"""
MongoDB Models for NEURA AI Assistant
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from mongoengine import (
    Document, EmbeddedDocument, StringField, IntField, 
    FloatField, DateTimeField, BooleanField, DictField, 
    ListField, ReferenceField, EmbeddedDocument<PERSON>ield,
    connect, disconnect
)
from config.settings import get_settings

# Initialize MongoDB connection
def init_mongodb():
    """Initialize MongoDB connection."""
    settings = get_settings()
    if hasattr(settings, 'mongodb_url'):
        connect(
            db=settings.mongodb_database,
            host=settings.mongodb_url,
            alias='default'
        )
    else:
        # Fallback for older config
        connect(
            db='neura_db',
            host='mongodb://localhost:27017',
            alias='default'
        )


class User(Document):
    """User profile and preferences."""
    name = StringField(required=True, max_length=100)
    email = StringField(required=True, unique=True, max_length=255)
    preferences = DictField(default=dict)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'users',
        'indexes': ['email', 'created_at']
    }
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)


class Memory(Document):
    """Persistent memory storage for user information."""
    user = ReferenceField(User, required=True)
    memory_type = StringField(required=True, max_length=50)
    key = StringField(required=True, max_length=255)
    value = StringField(required=True)
    metadata = DictField(default=dict)
    importance = FloatField(default=1.0, min_value=0.0, max_value=1.0)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    expires_at = DateTimeField()
    
    meta = {
        'collection': 'memories',
        'indexes': [
            'user',
            'memory_type',
            'key',
            'importance',
            'created_at',
            ('user', 'memory_type'),
            ('user', 'key')
        ]
    }
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)


class Conversation(Document):
    """Conversation history storage."""
    user = ReferenceField(User, required=True)
    session_id = StringField(required=True, max_length=255)
    message_type = StringField(required=True, max_length=20)  # user, assistant, system
    content = StringField(required=True)
    metadata = DictField(default=dict)
    timestamp = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'conversations',
        'indexes': [
            'user',
            'session_id',
            'timestamp',
            ('user', 'session_id'),
            ('user', 'timestamp')
        ]
    }


class WorkflowStep(EmbeddedDocument):
    """Individual workflow step."""
    step_number = IntField(required=True)
    title = StringField(required=True, max_length=255)
    description = StringField()
    status = StringField(default="pending", max_length=20)  # pending, in_progress, complete, skipped
    metadata = DictField(default=dict)
    started_at = DateTimeField()
    completed_at = DateTimeField()


class Workflow(Document):
    """Workflow definitions and tracking."""
    user = ReferenceField(User, required=True)
    name = StringField(required=True, max_length=255)
    description = StringField()
    status = StringField(default="active", max_length=20)  # active, paused, completed, cancelled
    current_step = IntField(default=0)
    total_steps = IntField(required=True)
    steps_data = DictField(required=True)  # Original step definitions
    steps = ListField(EmbeddedDocumentField(WorkflowStep))
    metadata = DictField(default=dict)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    completed_at = DateTimeField()
    
    meta = {
        'collection': 'workflows',
        'indexes': [
            'user',
            'status',
            'created_at',
            ('user', 'status')
        ]
    }
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)


class Reminder(Document):
    """Scheduled reminders and notifications."""
    user = ReferenceField(User, required=True)
    title = StringField(required=True, max_length=255)
    message = StringField(required=True)
    reminder_time = DateTimeField(required=True)
    is_recurring = BooleanField(default=False)
    recurrence_pattern = StringField(max_length=100)  # daily, weekly, etc.
    is_sent = BooleanField(default=False)
    created_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'reminders',
        'indexes': [
            'user',
            'reminder_time',
            'is_sent',
            ('user', 'reminder_time')
        ]
    }


class AIModelUsage(Document):
    """Track AI model usage and performance."""
    user = ReferenceField(User)
    model_name = StringField(required=True, max_length=255)
    task_type = StringField(required=True, max_length=100)  # summarization, qa, etc.
    input_tokens = IntField(default=0)
    output_tokens = IntField(default=0)
    response_time = FloatField()  # in seconds
    success = BooleanField(default=True)
    error_message = StringField()
    timestamp = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'ai_model_usage',
        'indexes': [
            'user',
            'model_name',
            'task_type',
            'timestamp',
            ('model_name', 'task_type')
        ]
    }


# Database utility functions
def get_user_by_email(email: str) -> Optional[User]:
    """Get user by email."""
    try:
        return User.objects(email=email).first()
    except Exception:
        return None


def create_user(name: str, email: str, preferences: Dict = None) -> User:
    """Create a new user."""
    user = User(
        name=name,
        email=email,
        preferences=preferences or {}
    )
    user.save()
    return user


def get_user_memories(user: User, memory_type: str = None, limit: int = 100) -> List[Memory]:
    """Get memories for a user."""
    query = Memory.objects(user=user)
    
    if memory_type:
        query = query.filter(memory_type=memory_type)
    
    # Filter out expired memories
    now = datetime.utcnow()
    query = query.filter(
        __raw__={
            '$or': [
                {'expires_at': {'$exists': False}},
                {'expires_at': None},
                {'expires_at': {'$gt': now}}
            ]
        }
    )
    
    return query.order_by('-importance', '-updated_at').limit(limit)


def search_memories(user: User, search_term: str, limit: int = 50) -> List[Memory]:
    """Search memories by content."""
    now = datetime.utcnow()
    
    return Memory.objects(
        user=user,
        __raw__={
            '$and': [
                {
                    '$or': [
                        {'key': {'$regex': search_term, '$options': 'i'}},
                        {'value': {'$regex': search_term, '$options': 'i'}}
                    ]
                },
                {
                    '$or': [
                        {'expires_at': {'$exists': False}},
                        {'expires_at': None},
                        {'expires_at': {'$gt': now}}
                    ]
                }
            ]
        }
    ).order_by('-importance').limit(limit)


def cleanup_expired_memories() -> int:
    """Remove expired memories and return count."""
    now = datetime.utcnow()
    expired_memories = Memory.objects(expires_at__lt=now)
    count = expired_memories.count()
    expired_memories.delete()
    return count


# Initialize MongoDB connection when module is imported
try:
    init_mongodb()
except Exception as e:
    print(f"Warning: Could not connect to MongoDB: {e}")
    print("Make sure MongoDB is running and accessible.")
