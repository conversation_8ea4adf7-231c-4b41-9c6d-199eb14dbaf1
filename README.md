# NEURA: Memory-Driven, Voice-Interactive, Workflow-Centric AI Assistant

NEUR<PERSON> is a comprehensive AI assistant designed to work like a human executive assistant and personal coach. It features persistent memory, voice interaction, workflow guidance, and Hugging Face-powered intelligence.

## Features

### 🧠 Persistent Memory
- Remembers user preferences, goals, and workflows across sessions
- Stores conversation history and context
- Learns from interactions to provide personalized assistance

### 🎤 Voice Interaction
- Speech-to-text input processing
- Natural text-to-speech responses
- Optimized for conversational, brief responses (<30 seconds)

### 📋 Workflow Guidance
- Multi-step process support (taxes, website creation, etc.)
- Progress tracking and status updates
- Automated reminders and follow-ups

### 🤖 AI-Powered Intelligence
- Hugging Face model integration
- Summarization, Q&A, and table processing
- Adaptive tone and style based on context

## Quick Start

### Prerequisites
- Python 3.8 or higher
- Microphone and speakers for voice interaction
- Internet connection for AI model access

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd NEURA
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your Hugging Face API token
```

4. Initialize the database:
```bash
python scripts/setup_database.py
```

5. Run NEURA:
```bash
python main.py
```

## Project Structure

```
NEURA/
├── main.py                 # Main application entry point
├── requirements.txt        # Python dependencies
├── .env.example           # Environment variables template
├── config/
│   ├── __init__.py
│   ├── settings.py        # Application configuration
│   └── models.py          # Database models
├── core/
│   ├── __init__.py
│   ├── memory.py          # Memory management system
│   ├── voice.py           # Voice input/output handling
│   ├── ai_engine.py       # Core AI logic and Hugging Face integration
│   └── workflow.py        # Workflow management
├── gui/
│   ├── __init__.py
│   ├── main_window.py     # Main GUI interface
│   └── components/        # GUI components
├── scripts/
│   ├── setup_database.py  # Database initialization
│   └── install.py         # Installation helper
└── tests/
    ├── __init__.py
    └── test_*.py          # Unit tests
```

## Usage

### Voice Commands
- "Hey NEURA, help me start a freelance business"
- "Remind me about this tomorrow at 7pm"
- "What's the latest on the UK visa process?"
- "Summarize this document"

### GUI Interface
- Click the microphone button to start voice input
- Type messages in the text input field
- View conversation history and workflow progress
- Access settings and memory management

## Configuration

Edit the `.env` file to configure:
- Hugging Face API token
- Voice settings (input/output devices)
- Memory storage location
- AI model preferences

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
