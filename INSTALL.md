# NEURA AI Assistant - Installation Guide

This guide will help you install and set up NEURA AI Assistant on your PC.

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **Python**: 3.8 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **Internet**: Required for AI model access
- **Audio**: Microphone and speakers for voice interaction

### Recommended Requirements
- **RAM**: 16GB for better performance
- **GPU**: NVIDIA GPU with CUDA support (optional, for faster AI processing)
- **Storage**: SSD for better database performance

## Quick Installation

### Option 1: Automated Installation (Recommended)

1. **Download NEURA**
   ```bash
   git clone https://github.com/your-username/neura-ai-assistant.git
   cd neura-ai-assistant
   ```

2. **Run Installation Script**
   ```bash
   python scripts/install.py
   ```

3. **Configure API Keys**
   - Edit the `.env` file created during installation
   - Add your Hugging Face API token (get one free at https://huggingface.co/settings/tokens)

4. **Start NEURA**
   ```bash
   python main.py
   ```

### Option 2: Manual Installation

1. **Clone Repository**
   ```bash
   git clone https://github.com/your-username/neura-ai-assistant.git
   cd neura-ai-assistant
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup Environment**
   ```bash
   cp .env.example .env
   # Edit .env file with your API keys
   ```

4. **Initialize Database**
   ```bash
   python scripts/setup_database.py
   ```

5. **Run NEURA**
   ```bash
   python main.py
   ```

## Platform-Specific Setup

### Windows

1. **Install Python** from https://python.org (3.8 or higher)
2. **Install Git** from https://git-scm.com/
3. **Install Microsoft Visual C++ Build Tools** (for some dependencies)
4. **Enable Microphone Access** in Windows Privacy Settings

### macOS

1. **Install Homebrew** (if not already installed):
   ```bash
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```

2. **Install Dependencies**:
   ```bash
   brew install python3 git portaudio
   ```

3. **Grant Microphone Permissions** in System Preferences > Security & Privacy

### Linux (Ubuntu/Debian)

1. **Install Dependencies**:
   ```bash
   sudo apt update
   sudo apt install python3 python3-pip git python3-pyaudio portaudio19-dev
   ```

2. **For other distributions**, install equivalent packages for Python 3, pip, git, and PortAudio

## Configuration

### 1. Hugging Face API Token

1. Go to https://huggingface.co/settings/tokens
2. Create a new token (read access is sufficient)
3. Add it to your `.env` file:
   ```
   HUGGINGFACE_API_TOKEN=your_token_here
   ```

### 2. Voice Settings

Edit `.env` file to configure voice settings:
```
VOICE_RATE=200          # Speech rate (words per minute)
VOICE_VOLUME=0.9        # Volume level (0.0 to 1.0)
VOICE_LANGUAGE=en       # Language code
```

### 3. Database Configuration

By default, NEURA uses SQLite. For advanced users, you can configure PostgreSQL or MySQL:
```
DATABASE_URL=postgresql://user:password@localhost/neura_db
```

## Running NEURA

### GUI Mode (Default)
```bash
python main.py
```

### Command Line Mode
```bash
python main.py cli
```

### Voice-Only Mode
```bash
python main.py voice
```

## Troubleshooting

### Common Issues

1. **"No module named 'transformers'"**
   - Solution: Run `pip install -r requirements.txt`

2. **Voice input not working**
   - Check microphone permissions
   - Install platform-specific audio dependencies
   - Test with `python -c "import speech_recognition; print('OK')"`

3. **"Database connection failed"**
   - Run `python scripts/setup_database.py --reset`

4. **Hugging Face API errors**
   - Verify your API token in `.env`
   - Check internet connection
   - Ensure token has proper permissions

5. **GUI not starting**
   - Install GUI dependencies: `pip install customtkinter pillow`
   - Try CLI mode: `python main.py cli`

### Performance Issues

1. **Slow AI responses**
   - Check internet connection
   - Consider using local models (advanced setup)
   - Upgrade to a faster internet plan

2. **High memory usage**
   - Close other applications
   - Restart NEURA periodically
   - Consider upgrading RAM

### Getting Help

1. **Check Logs**: Look in `logs/neura.log` for error details
2. **Test Installation**: Run `python main.py test`
3. **Community Support**: Visit our GitHub issues page
4. **Documentation**: Check the README.md for usage instructions

## Advanced Configuration

### Custom AI Models

You can configure different Hugging Face models in `.env`:
```
SUMMARIZATION_MODEL=facebook/bart-large-cnn
QA_MODEL=deepset/roberta-base-squad2
TABLE_MODEL=microsoft/tapex-large-finetuned-wtq
```

### Database Optimization

For better performance with large datasets:
```
DATABASE_ECHO=false
MAX_MEMORY_ENTRIES=1000
```

### Security Settings

For production use:
```
DEBUG=false
LOG_LEVEL=WARNING
```

## Updating NEURA

To update to the latest version:
```bash
git pull origin main
pip install -r requirements.txt --upgrade
python scripts/setup_database.py
```

## Uninstalling

To completely remove NEURA:
1. Delete the project directory
2. Remove the database file (`neura_memory.db`)
3. Clean up Python packages: `pip uninstall -r requirements.txt`

---

**Need help?** Check our [FAQ](README.md#faq) or create an issue on GitHub!
