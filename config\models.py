"""
Database models for NEURA AI Assistant
"""

from datetime import datetime
from typing import Op<PERSON>, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean, 
    JSON, ForeignKey, Float, create_engine
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from sqlalchemy.sql import func

from .settings import get_settings

Base = declarative_base()


class User(Base):
    """User profile and preferences."""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    email = Column(String(255), unique=True, index=True)
    preferences = Column(JSON, default={})
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    memories = relationship("Memory", back_populates="user")
    conversations = relationship("Conversation", back_populates="user")
    workflows = relationship("Workflow", back_populates="user")


class Memory(Base):
    """Persistent memory storage for user information."""
    __tablename__ = "memories"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    memory_type = Column(String(50), nullable=False)  # goal, preference, fact, etc.
    key = Column(String(255), nullable=False)
    value = Column(Text, nullable=False)
    metadata = Column(JSON, default={})
    importance = Column(Float, default=1.0)  # 0.0 to 1.0
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    expires_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="memories")


class Conversation(Base):
    """Conversation history storage."""
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    session_id = Column(String(255), nullable=False, index=True)
    message_type = Column(String(20), nullable=False)  # user, assistant, system
    content = Column(Text, nullable=False)
    metadata = Column(JSON, default={})
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="conversations")


class Workflow(Base):
    """Workflow definitions and tracking."""
    __tablename__ = "workflows"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    status = Column(String(20), default="active")  # active, paused, completed, cancelled
    current_step = Column(Integer, default=0)
    total_steps = Column(Integer, nullable=False)
    steps_data = Column(JSON, nullable=False)  # List of step definitions
    metadata = Column(JSON, default={})
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="workflows")
    steps = relationship("WorkflowStep", back_populates="workflow")


class WorkflowStep(Base):
    """Individual workflow step tracking."""
    __tablename__ = "workflow_steps"
    
    id = Column(Integer, primary_key=True, index=True)
    workflow_id = Column(Integer, ForeignKey("workflows.id"), nullable=False)
    step_number = Column(Integer, nullable=False)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    status = Column(String(20), default="pending")  # pending, in_progress, complete, skipped
    metadata = Column(JSON, default={})
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Relationships
    workflow = relationship("Workflow", back_populates="steps")


class Reminder(Base):
    """Scheduled reminders and notifications."""
    __tablename__ = "reminders"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    reminder_time = Column(DateTime, nullable=False)
    is_recurring = Column(Boolean, default=False)
    recurrence_pattern = Column(String(100), nullable=True)  # daily, weekly, etc.
    is_sent = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)


class AIModelUsage(Base):
    """Track AI model usage and performance."""
    __tablename__ = "ai_model_usage"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    model_name = Column(String(255), nullable=False)
    task_type = Column(String(100), nullable=False)  # summarization, qa, etc.
    input_tokens = Column(Integer, default=0)
    output_tokens = Column(Integer, default=0)
    response_time = Column(Float, nullable=True)  # in seconds
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow)


# Database engine and session setup
def create_database_engine():
    """Create database engine from settings."""
    settings = get_settings()
    engine = create_engine(
        settings.database_url,
        echo=settings.database_echo
    )
    return engine


def create_tables(engine):
    """Create all database tables."""
    Base.metadata.create_all(bind=engine)


def get_session_maker(engine):
    """Get SQLAlchemy session maker."""
    return sessionmaker(autocommit=False, autoflush=False, bind=engine)


# Global database setup
engine = create_database_engine()
SessionLocal = get_session_maker(engine)


def get_db():
    """Get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
